using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DLWSTransacLinea.Structures;
using DLWSTransacLinea.Reef.Services;

namespace EjemploUso
{
    /// <summary>
    /// Ejemplo de uso del servicio de cobranza masiva REEF
    /// </summary>
    public class EjemploUsoCollectMassive
    {
        public static async Task Main(string[] args)
        {
            try
            {
                // Simular datos de entrada como los que llegan del XML de solicitud
                
                // 1. Lista de requerimientos/recibos REEF
                var requerimientos = new List<xmlCobroTron.requerimiento>
                {
                    new xmlCobroTron.requerimiento { NUMRECI = "125384", SERIE = "", NUMEFACT = "" },
                    new xmlCobroTron.requerimiento { NUMRECI = "125385", SERIE = "", NUMEFACT = "" },
                    new xmlCobroTron.requerimiento { NUMRECI = "125386", SERIE = "", NUMEFACT = "" }
                };

                // 2. Lista de medios de pago
                var mediosPago = new List<xmlTransaccion.medio>
                {
                    new xmlTransaccion.medio
                    {
                        tipoPago = "EFE",
                        codEntidadFinanciera = "000",
                        numeroDocumento = "EFE001",
                        monto = "30.00",
                        codMoneda = "GTQ",
                        CUENTA_SIMPLIFICADA = "EF01",
                        DESCRIPCION = "Pago en efectivo"
                    },
                    new xmlTransaccion.medio
                    {
                        tipoPago = "TAR",
                        codEntidadFinanciera = "001",
                        numeroDocumento = "1234567890123456",
                        numeroReferencia = "REF001",
                        numeroAutorizacion = "AUTH123",
                        monto = "50.00",
                        codMoneda = "GTQ",
                        tipoTarjeta = "VISA",
                        codigoTarjeta = "VI",
                        CUENTA_SIMPLIFICADA = "TC01",
                        DESCRIPCION = "Pago con tarjeta VISA"
                    }
                };

                // 3. Encabezado de la transacción
                var encabezado = new xmlTransaccion._encabezado
                {
                    codigoCajero = "TRON2000",
                    usuarioOrigen = "USUARIO_TEST"
                };

                // 4. Bitácora (simulada)
                var bitacora = new Estructuras.Bitacora_wsTransacLinea
                {
                    SISTEMA = "REEF",
                    USUARIO_ORIGEN = "USUARIO_TEST",
                    METODO = "ProcesarCobranzaMasiva"
                };

                Console.WriteLine("=== EJEMPLO DE COBRANZA MASIVA REEF ===");
                Console.WriteLine($"Recibos a procesar: {requerimientos.Count}");
                Console.WriteLine($"Medios de pago: {mediosPago.Count}");
                Console.WriteLine($"Combinaciones totales (producto cartesiano): {requerimientos.Count * mediosPago.Count}");
                Console.WriteLine();

                // Mostrar las combinaciones que se van a procesar
                Console.WriteLine("COMBINACIONES A PROCESAR:");
                int combinacion = 1;
                foreach (var recibo in requerimientos)
                {
                    foreach (var medio in mediosPago)
                    {
                        Console.WriteLine($"  {combinacion}. Recibo: {recibo.NUMRECI} + Medio: {medio.tipoPago} ({medio.DESCRIPCION})");
                        combinacion++;
                    }
                }
                Console.WriteLine();

                // 5. Crear el servicio y procesar la cobranza masiva
                var collectMassiveService = new CollectMassiveService();
                
                Console.WriteLine("Iniciando procesamiento de cobranza masiva...");
                var resultado = await collectMassiveService.ProcesarCobranzaMasivaAsync(
                    requerimientos: requerimientos,
                    mediosPago: mediosPago,
                    encabezado: encabezado,
                    bitacora: bitacora,
                    btcMvmTypVal: 10,
                    canMvm: "N",
                    pocDat: DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    pgwVal: ""
                );

                // 6. Mostrar resultados
                Console.WriteLine("=== RESULTADOS DE LA COBRANZA MASIVA ===");
                Console.WriteLine($"Procesamiento exitoso: {(resultado.Exitoso ? "SÍ" : "NO")}");
                
                if (!string.IsNullOrEmpty(resultado.MensajeError))
                {
                    Console.WriteLine($"Mensaje de error: {resultado.MensajeError}");
                }

                Console.WriteLine($"Recibos procesados: {resultado.RecibosProcessados.Count}");
                Console.WriteLine();

                foreach (var reciboProcessado in resultado.RecibosProcessados)
                {
                    Console.WriteLine($"Recibo: {reciboProcessado.NumeroRecibo}");
                    Console.WriteLine($"  CAE: {reciboProcessado.CAE}");
                    Console.WriteLine($"  ID Cobro: {reciboProcessado.IdCobro}");
                    Console.WriteLine($"  Número Lote: {reciboProcessado.NumeroLote}");
                    Console.WriteLine($"  Fecha Procesamiento: {reciboProcessado.FechaProcesamiento}");
                    
                    if (!string.IsNullOrEmpty(reciboProcessado.MensajeError))
                    {
                        Console.WriteLine($"  Error: {reciboProcessado.MensajeError}");
                    }
                    Console.WriteLine();
                }

                Console.WriteLine("=== RESUMEN ===");
                var exitosos = resultado.RecibosProcessados.FindAll(r => string.IsNullOrEmpty(r.MensajeError)).Count;
                var conError = resultado.RecibosProcessados.Count - exitosos;
                
                Console.WriteLine($"Procesados exitosamente: {exitosos}");
                Console.WriteLine($"Con errores: {conError}");

                // 7. Mostrar información del endpoint utilizado
                Console.WriteLine("\n=== INFORMACIÓN TÉCNICA ===");
                Console.WriteLine("Endpoint utilizado: POST /newtron/api/treasury/receipt/collectMassive");
                Console.WriteLine("Parámetros de query:");
                Console.WriteLine("  - btcMvmTypVal: 10 (Tipo de movimiento de lote)");
                Console.WriteLine("  - canMvm: N (No cancelar movimiento)");
                Console.WriteLine("  - cmpVal: 2 (Compañía)");
                Console.WriteLine($"  - pocDat: {DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()} (Fecha de procesamiento)");
                Console.WriteLine("  - usrVal: TRON2000 (Usuario)");
                Console.WriteLine("  - pgwVal: '' (Gateway de pago vacío)");
                Console.WriteLine("\nEstructura del body JSON:");
                Console.WriteLine("  - oRcpMtgHedPT: Encabezados de recibos");
                Console.WriteLine("  - oRcpMtgPT: Items de recibos");
                Console.WriteLine("  - oRcpMtgCosPT: Costos de recibos");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error general: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine("\nPresiona cualquier tecla para salir...");
            Console.ReadKey();
        }
    }
}
