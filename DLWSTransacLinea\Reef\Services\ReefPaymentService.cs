using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DLWSTransacLinea.Structures;
using DLWSTransacLinea.Reef.Api;
using DLWSTransacLinea.Reef.BusinessLogic;
using DLWSTransacLinea.Reef.Models;
using Newtonsoft.Json;
using System.Linq;
using DLWSTransacLinea.Reef.Converters;

namespace DLWSTransacLinea.Reef.Services
{
    /// <summary>
    /// Servicio para procesar pagos digitales en REEF usando producto cartesiano
    /// </summary>
    public class ReefPaymentService
    {
        private readonly NwtTsyApiClient _apiClient;
        private readonly MonedaService _monedaService;

        public ReefPaymentService()
        {
            _apiClient = new NwtTsyApiClient();
            _monedaService = new MonedaService();
        }

        /// <summary>
        /// Procesa un pago digital simplificado realizando el producto cartesiano entre recibos y medios de pago
        /// </summary>
        /// <param name="requerimientos">Lista de requerimientos/recibos a pagar</param>
        /// <param name="mediosPago">Lista de medios de pago</param>
        /// <param name="encabezado">Información del encabezado de la transacción</param>
        /// <returns>Resultado del procesamiento del pago</returns>
        public async Task<bool> ProcesarPagoDigitalAsync(
            List<xmlCobroTron.requerimiento> requerimientos,
            List<xmlTransaccion.medio> mediosPago,
            xmlTransaccion._encabezado encabezado)
        {
            bool response = false;
            try
            {
                // Validaciones básicas
                if (requerimientos == null || requerimientos.Count == 0)
                {
                    //resultado.MensajeError = "No se proporcionaron requerimientos para procesar";
                    //return resultado;
                }

                if (mediosPago == null || mediosPago.Count == 0)
                {
                    //resultado.MensajeError = "No se proporcionaron medios de pago";
                    //return resultado;
                }

                var monedasREF = _monedaService.ConsultaMonedas();
                var digitalPayments = new List<DigitalPaymentSimplifiedBatch>();

                foreach (var recibo in requerimientos)
                {
                    foreach (var medio in mediosPago)
                    {
                        var monedaInformacion = monedasREF.Where(x => x.oCrnCrnS.sdrCrnVal == medio.codMoneda).FirstOrDefault();
                        var informacion_medio = new List<string>();

                        if (!string.IsNullOrWhiteSpace(medio.DESCRIPCION)) { informacion_medio.Add(medio.DESCRIPCION.Trim()); }
                        if (!string.IsNullOrWhiteSpace(medio.tipoPago)) { informacion_medio.Add($"MEDIO = {medio.tipoPago}"); }
                        if (!string.IsNullOrWhiteSpace(medio.tipoTarjeta)) { informacion_medio.Add($"TIPO TARJETA = {medio.tipoTarjeta}"); }
                        if (!string.IsNullOrWhiteSpace(medio.numeroReferencia)) { informacion_medio.Add($"REFERENCIA = {medio.numeroReferencia}"); }
                        if (!string.IsNullOrWhiteSpace(medio.numeroAutorizacion)) { informacion_medio.Add($"AUTORIZACION = {medio.numeroAutorizacion}"); }
                        if (!string.IsNullOrWhiteSpace(medio.fecha_cheque)) { informacion_medio.Add($"FECHA CHEQUE = {medio.fecha_cheque}"); }

                        string detalle_transaccion = string.Join(", ", informacion_medio);

                        var digitalPayment = new DigitalPaymentSimplifiedBatch
                        {
                            CloDat = Utils.DateTimeToUnixTimeStamp(DateTime.Today),
                            CloStsTypVal = "C",

                            // Datos de la compañía y configuración
                            CmpVal = int.Parse(Constants.GetCompany()),
                            LngVal = Constants.GetLanguage(),
                            UsrVal = encabezado?.codigoCajero,

                            // Documentos a pagar
                            CloAmn = double.Parse(medio.monto),
                            CloCrnVal = monedaInformacion.oCrnCrnS.crnVal,

                            RcpVal = encabezado.esAviso == "N" ? decimal.Parse(recibo.NUMRECI) : (decimal?)null,
                            RcpGrpVal = encabezado.esAviso == "S" ? decimal.Parse(recibo.NUMRECI) : (decimal?)null, //PENDIENTE
                            PlyVal = null,
                            QtnVal = null,
                            StlVal = null,

                            // Datos del medio de pago
                            CosMvmNam = detalle_transaccion,
                            CshVal = encabezado?.codigoCajero,
                            SmfAcoVal = medio.CUENTA_SIMPLIFICADA ?? "PENDIENTE",

                            // Anticipos
                            AdvTypVal = null,
                            AdvVal = null,

                            // Datos específicos
                            PgwVal = null,
                            PymDcmTypVal = null,
                            PymDcmVal = null,

                            // Datos adicionales
                            TknVal = null,
                            TnsVal = null,
                            MskVal = null,
                            PgwTxtVal = null
                        };

                        digitalPayments.Add(digitalPayment);
                    }
                }

                foreach (var digitalPayment in digitalPayments)
                {
                    string responseApi = await _apiClient.ProcessDigitalPaymentSimplifiedBatchAsync(digitalPayment);
                    response = true;
                }
            }
            catch (Exception ex)
            {
            }

            return response;
        }
    }
}
