using System;
using System.Collections.Generic;
using System.Configuration;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using DLWSTransacLinea.Reef.Models;
using DLWSTransacLinea.Structures;
using Newtonsoft.Json;

namespace DLWSTransacLinea.Reef.Api
{
    /// <summary>
    /// Cliente API para realizar llamadas a los servicios de tesoreria
    /// </summary>
    public class NwtTsyApiClient : BaseApiClient
    {
        /// <summary>
        /// Constructor que inicializa el cliente HTTP con la configuración del web.config
        /// </summary>
        public NwtTsyApiClient() : base("ReefApi_Tsy_BaseUrl", "ReefApi_Tsy_Username", "ReefApi_Tsy_Password")
        {
        }

        /// <summary>
        /// Consulta el recibo completo utilizando el endpoint getReceiptComplete
        /// </summary>
        /// <param name="receiptId">ID del recibo</param>
        /// <returns>Información del recibo en formato JSON</returns>
        public async Task<string> GetReceiptCompleteAsync(string receiptId)
        {
            try
            {
                string companyValue = Constants.GetCompany();
                string languageValue = Constants.GetLanguage();
                string userValue = Constants.GetUser();

                string url = $"newtron/api/treasury/receipt/{receiptId}?cmpVal={companyValue}&usrVal={userValue}";

                // Usar el nuevo método con retry automático y limpieza de caché
                using (var response = await SendRequestWithRetryAsync(() =>
                {
                    var request = CreateRequest(HttpMethod.Get, url);
                    request.Headers.Add("lngVal", languageValue);
                    return request;
                }))
                {
                    return await response.Content.ReadAsStringAsync();
                }
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"Error al consultar el recibo: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error inesperado: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Consulta el grupo de recibos utilizando el endpoint getReceiptGroup
        /// </summary>
        /// <param name="pymDcmTypVal">Tipo de documento de pago</param>
        /// <param name="pymDcmVal">Valor del documento de pago</param>
        /// <returns>Información del grupo de recibos en formato JSON</returns>
        public async Task<string> GetReceiptGroupAsync(string pymDcmTypVal, string pymDcmVal)
        {
            try
            {
                string companyValue = Constants.GetCompany();
                string languageValue = Constants.GetLanguage();
                string userValue = Constants.GetUser();

                string url = $"newtron/api/treasury/receiptGroup?cmpVal={companyValue}&pymDcmTypVal={pymDcmTypVal}&pymDcmVal={pymDcmVal}&usrVal={userValue}";

                // Usar el nuevo método con retry automático y limpieza de caché
                using (var response = await SendRequestWithRetryAsync(() =>
                {
                    var request = CreateRequest(HttpMethod.Get, url);
                    request.Headers.Add("lngVal", languageValue);
                    return request;
                }))
                {
                    return await response.Content.ReadAsStringAsync();
                }
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"Error al consultar Avisos de pago: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error inesperado: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Consulta los recibos de una póliza utilizando el endpoint querybyply
        /// </summary>
        /// <param name="policyNumber">Número de póliza</param>
        /// <param name="aplEnrSqn">Secuencia de aplicación del suplemento (por defecto 0)</param>
        /// <param name="aplVal">Valor de aplicación (por defecto 0)</param>
        /// <param name="enrSqn">Secuencia del suplemento (por defecto 0)</param>
        /// <returns>Información de los recibos en formato JSON</returns>
        public async Task<string> GetReceiptByPolicyAsync(string policyNumber, int aplEnrSqn = 0, int aplVal = 0, int enrSqn = 0)
        {
            try
            {
                string companyValue = Constants.GetCompany();
                string languageValue = Constants.GetLanguage();
                string userValue = Constants.GetUser();

                string url = "newtron/api/treasury/receipt/1.0/querybyply";

                // Crear el objeto de datos para el cuerpo de la petición
                var requestData = new
                {
                    aplEnrSqn = aplEnrSqn,
                    aplVal = aplVal,
                    enrSqn = enrSqn
                };

                string jsonContent = JsonConvert.SerializeObject(requestData);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                // Usar el nuevo método con retry automático y limpieza de caché
                using (var response = await SendRequestWithRetryAsync(() =>
                {
                    var request = CreateRequest(HttpMethod.Post, url);
                    request.Headers.Add("cmpVal", companyValue);
                    request.Headers.Add("lngVal", languageValue);
                    request.Headers.Add("plyVal", policyNumber);
                    request.Headers.Add("usrVal", userValue);
                    request.Content = content;
                    return request;
                }))
                {
                    return await response.Content.ReadAsStringAsync();
                }
            }
            catch (HttpRequestException ex)
            {
                throw new Exception(
                    $"Error al consultar los recibos de la póliza: {ex.Message}",
                    ex
                );
            }
            catch (Exception ex)
            {
                throw new Exception($"Error inesperado: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Procesa un pago digital simplificado por lotes utilizando el endpoint digitalPayment/simplifiedBatch
        /// </summary>
        /// <param name="request">Objeto con todos los parámetros necesarios para el procesamiento del pago digital</param>
        /// <returns>Respuesta del procesamiento del pago digital en formato JSON</returns>
        public async Task<string> ProcessDigitalPaymentSimplifiedBatchAsync(DigitalPaymentSimplifiedBatch request)
        {
            try
            {
                // Validar que el request no sea nulo
                if (request == null)
                    throw new ArgumentNullException(nameof(request), "El objeto request no puede ser nulo");

                string companyValue = Constants.GetCompany();
                string languageValue = Constants.GetLanguage();
                string userValue = Constants.GetUser();

                // Construir la URL con todos los parámetros de query - TODOS los campos se envían siempre
                string url = "newtron/api/treasury/digitalPayment/simplifiedBatch?" +
                           $"cloDat={request.CloDat?.ToString() ?? ""}&" +
                           $"cloStsTypVal={Uri.EscapeDataString(request.CloStsTypVal ?? "")}&" +
                           $"cmpVal={companyValue}&" +
                           $"advTypVal={Uri.EscapeDataString(request.AdvTypVal ?? "")}&" +
                           $"advVal={Uri.EscapeDataString(request.AdvVal ?? "")}&" +
                           $"cloAmn={request.CloAmn?.ToString(System.Globalization.CultureInfo.InvariantCulture) ?? ""}&" +
                           $"cloCrnVal={request.CloCrnVal?.ToString() ?? ""}&" +
                           $"cosMvmNam={Uri.EscapeDataString(request.CosMvmNam ?? "")}&" +
                           $"cshVal={Uri.EscapeDataString(request.CshVal ?? "")}&" +
                           $"lngVal={languageValue}&" +
                           $"mskVal={Uri.EscapeDataString(request.MskVal ?? "")}&" +
                           $"pgwVal={Uri.EscapeDataString(request.PgwVal ?? "")}&" +
                           $"plyVal={Uri.EscapeDataString(request.PlyVal ?? "")}&" +
                           $"pymDcmTypVal={Uri.EscapeDataString(request.PymDcmTypVal ?? "")}&" +
                           $"pymDcmVal={Uri.EscapeDataString(request.PymDcmVal ?? "")}&" +
                           $"qtnVal={Uri.EscapeDataString(request.QtnVal ?? "")}&" +
                           $"rcpVal={request.RcpVal?.ToString() ?? ""}&" +
                           $"smfAcoVal={Uri.EscapeDataString(request.SmfAcoVal ?? "")}&" +
                           $"stlVal={request.StlVal?.ToString() ?? ""}&" +
                           $"tknVal={Uri.EscapeDataString(request.TknVal ?? "")}&" +
                           $"tnsVal={Uri.EscapeDataString(request.TnsVal ?? "")}&" +
                           $"usrVal={userValue}";

                // Crear el contenido JSON - pgwTxtVal va en el body según el Swagger
                var requestData = request.PgwTxtVal ?? new { };
                string jsonContent = JsonConvert.SerializeObject(requestData);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                // Usar el nuevo método con retry automático y limpieza de caché
                using (var response = await SendRequestWithRetryAsync(() =>
                {
                    var httpRequest = CreateRequest(HttpMethod.Post, url);
                    httpRequest.Content = content;
                    return httpRequest;
                }))
                {
                    return await response.Content.ReadAsStringAsync();
                }
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"Error al procesar el pago digital simplificado: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error inesperado: {ex.Message}", ex);
            }
        }
    
    }
}
