﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{8A675A77-D1C8-4A39-9F01-06D9B0FC9A33}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DLWSTransacLinea</RootNamespace>
    <AssemblyName>DLWSTransacLinea</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=9.0.0.5, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.5\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=9.0.0.5, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.9.0.5\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=9.0.0.5, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.9.0.5\lib\net462\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="MySqlConnector, Version=1.0.0.0, Culture=neutral, PublicKeyToken=d33d3e53aa5f8c92, processorArchitecture=MSIL">
      <HintPath>..\packages\MySqlConnector.1.3.14\lib\net471\MySqlConnector.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.DataAccess, Version=4.121.1.0, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\ZonaAliados\Lbl_Cotizado_Autos_Web\Librerias\Oracle.DataAccess.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=9.0.0.5, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.9.0.5\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.IO, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Context" />
    <Reference Include="System.Runtime, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.0\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.1.2\lib\net462\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceModel.Channels" />
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.6.3\lib\net462\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Comunes\CalculoModule.cs" />
    <Compile Include="Comunes\GeneraModule.cs" />
    <Compile Include="Comunes\ValidacionModule.cs" />
    <Compile Include="Cores\AcselDB.cs" />
    <Compile Include="Cores\MySQLLogger.cs" />
    <Compile Include="Reef\Services\ReefPaymentService.cs" />
    <Compile Include="Reef\Models\DigitalPaymentSimplifiedBatch.cs" />
    <Compile Include="Structures\POS\cobroPOS.cs" />
    <Compile Include="Structures\POS\DEBITO_SAS.cs" />
    <Compile Include="Structures\POS\respuestaPOS.cs" />
    <Compile Include="Structures\xmlBusqueda.cs" />
    <Compile Include="Structures\xmlPagoRequermiento.cs" />
    <Compile Include="Structures\xmlRequerimientos.cs" />
    <Compile Include="Comunes\Consultas.cs" />
    <Compile Include="Comunes\Estructuras.cs" />
    <Compile Include="Comunes\Logger.cs" />
    <Compile Include="Comunes\transaccion.cs" />
    <Compile Include="Structures\xmlTransaccion.cs" />
    <Compile Include="Cores\ConexionBD.cs" />
    <Compile Include="Cores\TronwebDB.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Reef\Api\BaseApiClient.cs" />
    <Compile Include="Reef\Api\CacheClearService.cs" />
    <Compile Include="Reef\Api\Constants.cs" />
    <Compile Include="Reef\Api\NwtGtApiClient.cs" />
    <Compile Include="Reef\Api\NwtCmnApiClient.cs" />
    <Compile Include="Reef\Api\NwtTsyApiClient.cs" />
    <Compile Include="Reef\Api\NwtIsuApiClient.cs" />
    <Compile Include="Reef\Api\SslConfiguration.cs" />
    <Compile Include="Reef\BusinessLogic\CobranzaReef.cs" />
    <Compile Include="Reef\BusinessLogic\ConsultasReef.cs" />
    <Compile Include="Reef\BusinessLogic\DeducibleReef.cs" />
    <Compile Include="Reef\BusinessLogic\DocumentacionReef.cs" />
    <Compile Include="Reef\Converters\AvisoPagoPolizaGrupoConverter.cs" />
    <Compile Include="Reef\Converters\DeducibleConverter.cs" />
    <Compile Include="Reef\Converters\PolizaConverter.cs" />
    <Compile Include="Reef\Converters\AvisoPagoConverter.cs" />
    <Compile Include="Reef\Converters\PolizaGrupoConverter.cs" />
    <Compile Include="Reef\Converters\ReciboConverter.cs" />
    <Compile Include="Reef\Converters\Utils.cs" />
    <Compile Include="Reef\Services\AvisoPagoService.cs" />
    <Compile Include="Reef\Services\DeducibleService.cs" />
    <Compile Include="Reef\Services\PolizaGrupoService.cs" />
    <Compile Include="Reef\Services\TerceroService.cs" />
    <Compile Include="Reef\Models\AvisoPagoPolizaGrupoStructure.cs" />
    <Compile Include="Reef\Models\AvisoPagoStructure.cs" />
    <Compile Include="Reef\Services\MonedaService.cs" />
    <Compile Include="Reef\Services\ReciboService.cs" />
    <Compile Include="Reef\Services\PolizaService.cs" />
    <Compile Include="Reef\Models\ContratoPolizaGrupoStructure.cs" />
    <Compile Include="Reef\Models\DeducibleInfoStructure.cs" />
    <Compile Include="Reef\Models\InfoReciboStructure.cs" />
    <Compile Include="Reef\Models\MonedaStructure.cs" />
    <Compile Include="Reef\Models\MontoDeducibleStructure.cs" />
    <Compile Include="Reef\Models\PolizaStructure.cs" />
    <Compile Include="Reef\Models\ReciboStructure.cs" />
    <Compile Include="Reef\Models\ResponsablePagoStructure.cs" />
    <Compile Include="Reef\Models\ValidacionDeducibleStructure.cs" />
    <Compile Include="Service References\WSComunica\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Structures\xmlBitacora.cs" />
    <Compile Include="Structures\xmlCobroTron.cs" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Libs\MySql.Data.dll" />
    <Content Include="Libs\Oracle.DataAccess.dll" />
    <None Include="Service References\WSComunica\enviar_correo.disco" />
    <None Include="Service References\WSComunica\enviar_correo.wsdl" />
    <None Include="Service References\WSComunica\enviar_correo.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\WSComunica\enviar_correo1.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\WSComunica\enviar_correo2.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\WSComunica\enviar_correo3.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\WSComunica\enviar_correo4.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\WSComunica\enviar_correo5.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\WSComunica\enviar_correo6.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\WSComunica\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Service References\WSComunica\configuration.svcinfo" />
    <None Include="Service References\WSComunica\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadataStorage Include="Service References\WSComunica\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>