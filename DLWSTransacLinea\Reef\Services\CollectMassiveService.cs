using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DLWSTransacLinea.Structures;
using DLWSTransacLinea.Reef.Api;
using DLWSTransacLinea.Reef.Structures;
using Newtonsoft.Json;

namespace DLWSTransacLinea.Reef.Services
{
    /// <summary>
    /// Servicio para procesar cobranzas masivas en REEF
    /// </summary>
    public class CollectMassiveService
    {
        private readonly NwtTsyApiClient _apiClient;

        public CollectMassiveService()
        {
            _apiClient = new NwtTsyApiClient();
        }

        /// <summary>
        /// Procesa una cobranza masiva de recibos
        /// </summary>
        /// <param name="requerimientos">Lista de requerimientos/recibos a cobrar</param>
        /// <param name="mediosPago">Lista de medios de pago</param>
        /// <param name="encabezado">Información del encabezado de la transacción</param>
        /// <param name="bitacora">Objeto de bitácora para logging</param>
        /// <param name="btcMvmTypVal">Tipo de movimiento de lote</param>
        /// <param name="canMvm">Cancelar movimiento</param>
        /// <param name="pocDat">Fecha de procesamiento</param>
        /// <param name="pgwVal">Gateway de pago</param>
        /// <returns>Resultado del procesamiento de la cobranza masiva</returns>
        public async Task<ResultadoCobranzaMasiva> ProcesarCobranzaMasivaAsync(
            List<xmlCobroTron.requerimiento> requerimientos,
            List<xmlTransaccion.medio> mediosPago,
            xmlTransaccion._encabezado encabezado,
            Estructuras.Bitacora_wsTransacLinea bitacora,
            int btcMvmTypVal = 10,
            string canMvm = "N",
            long? pocDat = null,
            string pgwVal = "")
        {
            var resultado = new ResultadoCobranzaMasiva();

            try
            {
                // Validaciones básicas
                if (requerimientos == null || requerimientos.Count == 0)
                {
                    resultado.MensajeError = "No se proporcionaron requerimientos para procesar";
                    return resultado;
                }

                if (mediosPago == null || mediosPago.Count == 0)
                {
                    resultado.MensajeError = "No se proporcionaron medios de pago";
                    return resultado;
                }

                // Construir el request para la cobranza masiva
                var request = ConstruirRequestCobranzaMasiva(requerimientos, mediosPago, encabezado);

                // Llamar al endpoint de cobranza masiva
                string respuestaJson = await _apiClient.PostCollectMassiveAsync(
                    request: request,
                    btcMvmTypVal: btcMvmTypVal,
                    canMvm: canMvm,
                    pocDat: pocDat,
                    pgwVal: pgwVal
                );

                // Procesar respuesta
                resultado = ProcesarRespuestaCobranzaMasiva(respuestaJson, requerimientos);

                Console.WriteLine($"Cobranza masiva procesada exitosamente. Recibos procesados: {resultado.RecibosProcessados.Count}");
            }
            catch (Exception ex)
            {
                resultado.Exitoso = false;
                resultado.MensajeError = $"Error general en el procesamiento de cobranza masiva: {ex.Message}";
                Console.WriteLine($"Error general en CollectMassiveService: {ex.Message}");
            }

            return resultado;
        }

        /// <summary>
        /// Construye el request para la cobranza masiva
        /// </summary>
        private CollectMassiveRequest ConstruirRequestCobranzaMasiva(
            List<xmlCobroTron.requerimiento> requerimientos,
            List<xmlTransaccion.medio> mediosPago,
            xmlTransaccion._encabezado encabezado)
        {
            var request = new CollectMassiveRequest();

            // Procesar cada requerimiento con cada medio de pago (producto cartesiano)
            foreach (var requerimiento in requerimientos)
            {
                foreach (var medio in mediosPago)
                {
                    // Crear item base con datos comunes
                    var itemBase = ConstruirItemBase(requerimiento, medio, encabezado);

                    // Agregar a las tres listas (según la estructura del CURL)
                    request.oRcpMtgHedPT.Add(new ReceiptMassiveHeader
                    {
                        chtBnkTypVal = itemBase.chtBnkTypVal,
                        cloPymTypVal = itemBase.cloPymTypVal,
                        cloStsTypVal = itemBase.cloStsTypVal,
                        cmpVal = itemBase.cmpVal,
                        cosIdnVal = itemBase.cosIdnVal,
                        cosCrnVal = itemBase.cosCrnVal,
                        crnVal = itemBase.crnVal,
                        cshVal = itemBase.cshVal,
                        mtcVal = itemBase.mtcVal,
                        obsVal = itemBase.obsVal,
                        pgwTxtVal = itemBase.pgwTxtVal,
                        pgwVal = itemBase.pgwVal,
                        pocDat = itemBase.pocDat,
                        prcTrmVal = itemBase.prcTrmVal,
                        rcpAmn = itemBase.rcpAmn,
                        rcpVal = itemBase.rcpVal,
                        smfAcoTypVal = itemBase.smfAcoTypVal,
                        smfAcoVal = itemBase.smfAcoVal,
                        sndDat = itemBase.sndDat,
                        usrVal = itemBase.usrVal
                    });

                    request.oRcpMtgPT.Add(new ReceiptMassiveItem
                    {
                        chtBnkTypVal = itemBase.chtBnkTypVal,
                        cloPymTypVal = itemBase.cloPymTypVal,
                        cloStsTypVal = itemBase.cloStsTypVal,
                        cmpVal = itemBase.cmpVal,
                        cosIdnVal = itemBase.cosIdnVal,
                        cosCrnVal = itemBase.cosCrnVal,
                        crnVal = itemBase.crnVal,
                        cshVal = itemBase.cshVal,
                        mtcVal = itemBase.mtcVal,
                        obsVal = itemBase.obsVal,
                        pgwTxtVal = itemBase.pgwTxtVal,
                        pgwVal = itemBase.pgwVal,
                        pocDat = itemBase.pocDat,
                        prcTrmVal = itemBase.prcTrmVal,
                        rcpAmn = itemBase.rcpAmn,
                        rcpVal = itemBase.rcpVal,
                        smfAcoTypVal = itemBase.smfAcoTypVal,
                        smfAcoVal = itemBase.smfAcoVal,
                        sndDat = itemBase.sndDat,
                        usrVal = itemBase.usrVal
                    });

                    request.oRcpMtgCosPT.Add(new ReceiptMassiveCost
                    {
                        chtBnkTypVal = itemBase.chtBnkTypVal,
                        cloPymTypVal = itemBase.cloPymTypVal,
                        cloStsTypVal = itemBase.cloStsTypVal,
                        cmpVal = itemBase.cmpVal,
                        cosIdnVal = itemBase.cosIdnVal,
                        cosCrnVal = itemBase.cosCrnVal,
                        crnVal = itemBase.crnVal,
                        cshVal = itemBase.cshVal,
                        mtcVal = itemBase.mtcVal,
                        obsVal = itemBase.obsVal,
                        pgwTxtVal = itemBase.pgwTxtVal,
                        pgwVal = itemBase.pgwVal,
                        pocDat = itemBase.pocDat,
                        prcTrmVal = itemBase.prcTrmVal,
                        rcpAmn = itemBase.rcpAmn,
                        rcpVal = itemBase.rcpVal,
                        smfAcoTypVal = itemBase.smfAcoTypVal,
                        smfAcoVal = itemBase.smfAcoVal,
                        sndDat = itemBase.sndDat,
                        usrVal = itemBase.usrVal
                    });
                }
            }

            return request;
        }

        /// <summary>
        /// Construye un item base con los datos comunes para la cobranza masiva
        /// </summary>
        private ReceiptMassiveHeader ConstruirItemBase(
            xmlCobroTron.requerimiento requerimiento,
            xmlTransaccion.medio medio,
            xmlTransaccion._encabezado encabezado)
        {
            // Obtener monto del medio de pago o usar valor por defecto
            decimal monto = decimal.TryParse(medio.monto, out var montoDecimal) ? montoDecimal : 30m;

            // Obtener número de recibo
            int numeroRecibo = int.TryParse(requerimiento.NUMRECI, out var rcpVal) ? rcpVal : 125384;

            return new ReceiptMassiveHeader
            {
                chtBnkTypVal = "C", // Tipo de banco de cheque
                cloPymTypVal = "C", // Tipo de pago de cobranza
                cloStsTypVal = "4", // Tipo de situación de cobranza
                cmpVal = 2, // Compañía
                cosIdnVal = 1234, // Valor de identificación de costo
                cosCrnVal = ObtenerIdMoneda(medio.codMoneda), // Valor de moneda de costo
                crnVal = ObtenerIdMoneda(medio.codMoneda), // Valor de moneda
                cshVal = encabezado?.codigoCajero ?? "TRON2000", // Cajero
                mtcVal = 2, // Valor de método
                obsVal = $"Cobranza masiva - Recibo {requerimiento.NUMRECI}", // Observaciones
                pgwTxtVal = $"Cobranza masiva API - {medio.tipoPago}", // Texto del gateway de pago
                pgwVal = MapearGatewayPago(medio.tipoPago), // Gateway de pago
                pocDat = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString(), // Fecha de procesamiento
                prcTrmVal = "0", // Valor de término de proceso
                rcpAmn = monto, // Monto del recibo
                rcpVal = numeroRecibo, // Valor del recibo
                smfAcoTypVal = ObtenerTipoCuentaSimplificada(medio.tipoPago), // Tipo de cuenta simplificada
                smfAcoVal = medio.CUENTA_SIMPLIFICADA ?? ObtenerCuentaSimplificada(medio.tipoPago), // Valor de cuenta simplificada
                sndDat = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString(), // Fecha de envío
                usrVal = encabezado?.codigoCajero ?? "TRON2000" // Usuario
            };
        }

        /// <summary>
        /// Obtiene el ID de moneda basado en el código
        /// </summary>
        private int ObtenerIdMoneda(string codigoMoneda)
        {
            switch (codigoMoneda?.ToUpper())
            {
                case "GTQ":
                case "Q":
                    return 4; // Quetzal
                case "USD":
                case "$":
                    return 1; // Dólar
                case "EUR":
                    return 2; // Euro
                default:
                    return 4; // Por defecto Quetzal
            }
        }

        /// <summary>
        /// Mapea el tipo de pago a un gateway específico
        /// </summary>
        private string MapearGatewayPago(string tipoPago)
        {
            switch (tipoPago?.ToUpper())
            {
                case "TAR":
                case "TARJETA":
                    return "PAYU";
                case "EFE":
                case "EFECTIVO":
                    return "";
                case "CHE":
                case "CHEQUE":
                    return "CHEQUE";
                case "DEP":
                case "DEPOSITO":
                    return "DEPOSITO";
                case "TRA":
                case "TRANSFERENCIA":
                    return "TRANSFERENCIA";
                default:
                    return "";
            }
        }

        /// <summary>
        /// Obtiene el tipo de cuenta simplificada basado en el tipo de pago
        /// </summary>
        private string ObtenerTipoCuentaSimplificada(string tipoPago)
        {
            switch (tipoPago?.ToUpper())
            {
                case "EFE":
                case "EFECTIVO":
                    return "EF";
                case "TAR":
                case "TARJETA":
                    return "TC";
                case "CHE":
                case "CHEQUE":
                    return "CH";
                case "DEP":
                case "DEPOSITO":
                    return "DP";
                case "TRA":
                case "TRANSFERENCIA":
                    return "TR";
                default:
                    return "EF";
            }
        }

        /// <summary>
        /// Obtiene la cuenta simplificada basada en el tipo de pago
        /// </summary>
        private string ObtenerCuentaSimplificada(string tipoPago)
        {
            switch (tipoPago?.ToUpper())
            {
                case "EFE":
                case "EFECTIVO":
                    return "EF01";
                case "TAR":
                case "TARJETA":
                    return "TC01";
                case "CHE":
                case "CHEQUE":
                    return "CH01";
                case "DEP":
                case "DEPOSITO":
                    return "DP01";
                case "TRA":
                case "TRANSFERENCIA":
                    return "TR01";
                default:
                    return "EF01";
            }
        }

        /// <summary>
        /// Procesa la respuesta de la cobranza masiva
        /// </summary>
        private ResultadoCobranzaMasiva ProcesarRespuestaCobranzaMasiva(string respuestaJson, List<xmlCobroTron.requerimiento> requerimientos)
        {
            var resultado = new ResultadoCobranzaMasiva();

            try
            {
                // Intentar deserializar la respuesta
                dynamic respuesta = JsonConvert.DeserializeObject(respuestaJson);

                // Procesar cada requerimiento
                foreach (var requerimiento in requerimientos)
                {
                    var reciboProcessado = new ReciboProcessadoMasivo
                    {
                        NumeroRecibo = requerimiento.NUMRECI
                    };

                    // Extraer información relevante de la respuesta
                    // Nota: La estructura exacta depende de la respuesta real del API
                    if (respuesta?.success == true || respuesta?.codigo?.ToString() == "00")
                    {
                        reciboProcessado.CAE = respuesta?.cae?.ToString() ?? "PENDIENTE";
                        reciboProcessado.IdCobro = respuesta?.idCobro?.ToString() ?? "PENDIENTE";
                        reciboProcessado.NumeroLote = respuesta?.numeroLote?.ToString() ?? "PENDIENTE";
                        reciboProcessado.FechaProcesamiento = DateTime.Now;
                    }
                    else
                    {
                        reciboProcessado.CAE = "PENDIENTE";
                        reciboProcessado.IdCobro = "PENDIENTE";
                        reciboProcessado.NumeroLote = "PENDIENTE";
                        reciboProcessado.MensajeError = respuesta?.mensaje?.ToString() ?? "Error desconocido en el procesamiento";
                        reciboProcessado.FechaProcesamiento = DateTime.Now;
                    }

                    resultado.RecibosProcessados.Add(reciboProcessado);
                }

                // Determinar si el procesamiento fue exitoso
                resultado.Exitoso = resultado.RecibosProcessados.Count > 0 &&
                                   resultado.RecibosProcessados.TrueForAll(r => string.IsNullOrEmpty(r.MensajeError));

                if (!resultado.Exitoso && resultado.RecibosProcessados.Any(r => !string.IsNullOrEmpty(r.MensajeError)))
                {
                    resultado.MensajeError = "Algunos recibos no pudieron ser procesados correctamente";
                }
            }
            catch (Exception ex)
            {
                // Si no se puede parsear la respuesta, marcar todos como error
                resultado.Exitoso = false;
                resultado.MensajeError = $"Error procesando respuesta de cobranza masiva: {ex.Message}";

                foreach (var requerimiento in requerimientos)
                {
                    resultado.RecibosProcessados.Add(new ReciboProcessadoMasivo
                    {
                        NumeroRecibo = requerimiento.NUMRECI,
                        CAE = "PENDIENTE",
                        IdCobro = "PENDIENTE",
                        NumeroLote = "PENDIENTE",
                        MensajeError = "Error procesando respuesta",
                        FechaProcesamiento = DateTime.Now
                    });
                }
            }

            return resultado;
        }
    }
}
