﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DLWSTransacLinea.Reef.Converters
{
    public class Utils
    {
        /// <summary>
        /// Convierte un timestamp Unix a DateTime
        /// </summary>
        /// <param name="unixTimeStamp">Timestamp en formato Unix</param>
        /// <returns>Fecha en formato DateTime</returns>
        public static DateTime UnixTimeStampToDateTime(long unixTimeStamp)
        {
            DateTime dateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            DateTime utcDateTime = dateTime.AddMilliseconds(unixTimeStamp);
            return utcDateTime;
        }

        /// <summary>
        /// Convierte un objeto DateTime a timestamp Unix (milisegundos desde 1970-01-01 UTC)
        /// </summary>
        /// <param name="dateTime">Fecha en formato DateTime</param>
        /// <returns>Timestamp en formato Unix (milisegundos)</returns>
        public static long DateTimeToUnixTimeStamp(DateTime dateTime)
        {
            DateTime epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            TimeSpan timeSpan = dateTime.ToUniversalTime() - epoch;
            return (long)timeSpan.TotalMilliseconds;
        }
    }
}
