using System;
using System.Collections.Generic;

namespace DLWSTransacLinea.Reef.Models
{
    /// <summary>
    /// Estructura para el procesamiento de cobranza masiva
    /// </summary>
    public class CollectMassiveRequest
    {
        /// <summary>
        /// Lista de encabezados de recibos para cobranza masiva
        /// </summary>
        public List<ReceiptMassiveHeader> oRcpMtgHedPT { get; set; }

        /// <summary>
        /// Lista de recibos para cobranza masiva
        /// </summary>
        public List<ReceiptMassiveItem> oRcpMtgPT { get; set; }

        /// <summary>
        /// Lista de costos para cobranza masiva
        /// </summary>
        public List<ReceiptMassiveCost> oRcpMtgCosPT { get; set; }

        public CollectMassiveRequest()
        {
            oRcpMtgHedPT = new List<ReceiptMassiveHeader>();
            oRcpMtgPT = new List<ReceiptMassiveItem>();
            oRcpMtgCosPT = new List<ReceiptMassiveCost>();
        }
    }

    /// <summary>
    /// Encabezado de recibo para cobranza masiva
    /// </summary>
    public class ReceiptMassiveHeader
    {
        /// <summary>
        /// Tipo de Movimiento (caja / Banco U Otros)
        /// </summary>
        public string chtBnkTypVal { get; set; }

        /// <summary>
        /// Tipo Cobro Pago
        /// </summary>
        public string cloPymTypVal { get; set; }

        /// <summary>
        /// Tipo Situación Cobro
        /// </summary>
        public string cloStsTypVal { get; set; }

        /// <summary>
        /// Compañía
        /// </summary>
        public int cmpVal { get; set; }

        /// <summary>
        /// Identificador de compensación para el cobro
        /// </summary>
        public int cosIdnVal { get; set; }

        /// <summary>
        /// Código moneda compensación
        /// </summary>
        public int cosCrnVal { get; set; }

        /// <summary>
        /// Moneda
        /// </summary>
        public int crnVal { get; set; }

        /// <summary>
        /// Cajero
        /// </summary>
        public string cshVal { get; set; }

        /// <summary>
        /// Forma en la Que el Tercero Cobra o Paga
        /// </summary>
        public int mtcVal { get; set; }

        /// <summary>
        /// Observaciones
        /// </summary>
        public string obsVal { get; set; }

        /// <summary>
        /// Clob recibido de la pasarela de pago
        /// </summary>
        public string pgwTxtVal { get; set; }

        /// <summary>
        /// Pasarela de Pago
        /// </summary>
        public string pgwVal { get; set; }

        /// <summary>
        /// Fecha en la Que Se Realiza el Proceso Masivo
        /// </summary>
        public string pocDat { get; set; }

        /// <summary>
        /// Terminación
        /// </summary>
        public string prcTrmVal { get; set; }

        /// <summary>
        /// Total del Recibo
        /// </summary>
        public decimal rcpAmn { get; set; }

        /// <summary>
        /// Recibo
        /// </summary>
        public int rcpVal { get; set; }

        /// <summary>
        /// Tipo de Cuenta Simplificada
        /// </summary>
        public string smfAcoTypVal { get; set; }

        /// <summary>
        /// Cuenta Simplificada
        /// </summary>
        public string smfAcoVal { get; set; }

        /// <summary>
        /// Fecha Envío
        /// </summary>
        public string sndDat { get; set; }

        /// <summary>
        /// Usuario
        /// </summary>
        public string usrVal { get; set; }
    }

    /// <summary>
    /// Item de recibo para cobranza masiva
    /// </summary>
    public class ReceiptMassiveItem : ReceiptMassiveHeader
    {
        // Hereda todas las propiedades de ReceiptMassiveHeader
        // Se mantiene como clase separada para claridad en la estructura del JSON
    }

    /// <summary>
    /// Costo de recibo para cobranza masiva
    /// </summary>
    public class ReceiptMassiveCost : ReceiptMassiveHeader
    {
        // Hereda todas las propiedades de ReceiptMassiveHeader
        // Se mantiene como clase separada para claridad en la estructura del JSON
    }
}
