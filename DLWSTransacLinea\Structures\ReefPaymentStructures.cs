using System;
using System.Collections.Generic;

namespace DLWSTransacLinea.Structures
{
    /// <summary>
    /// Resultado del procesamiento de pago en REEF
    /// </summary>
    public class ResultadoPagoReef
    {
        public bool Exitoso { get; set; }
        public string MensajeError { get; set; }
        public List<ReciboProcessado> RecibosProcessados { get; set; }

        public ResultadoPagoReef()
        {
            RecibosProcessados = new List<ReciboProcessado>();
        }
    }

    /// <summary>
    /// Información de un recibo procesado
    /// </summary>
    public class ReciboProcessado
    {
        public string NumeroRecibo { get; set; }
        public string CAE { get; set; }
        public string IdCobro { get; set; }
        public string MensajeError { get; set; }
    }

    /// <summary>
    /// Información detallada de un recibo
    /// </summary>
    public class ReciboInfo
    {
        public string NumeroRecibo { get; set; }
        public string Poliza { get; set; }
        public string Moneda { get; set; }
        public int IdMoneda { get; set; }
        public decimal Total { get; set; }
        public DateTime FechaVencimiento { get; set; }
        public string Estado { get; set; }
    }

    /// <summary>
    /// Combinación de recibo y medio de pago para el producto cartesiano
    /// </summary>
    public class CombinacionPago
    {
        public ReciboInfo Recibo { get; set; }
        public xmlTransaccion.medio MedioPago { get; set; }
    }

    /// <summary>
    /// Resultado del procesamiento de cobranza masiva en REEF
    /// </summary>
    public class ResultadoCobranzaMasiva
    {
        public bool Exitoso { get; set; }
        public string MensajeError { get; set; }
        public List<ReciboProcessadoMasivo> RecibosProcessados { get; set; }

        public ResultadoCobranzaMasiva()
        {
            RecibosProcessados = new List<ReciboProcessadoMasivo>();
        }
    }

    /// <summary>
    /// Información de un recibo procesado en cobranza masiva
    /// </summary>
    public class ReciboProcessadoMasivo
    {
        public string NumeroRecibo { get; set; }
        public string CAE { get; set; }
        public string IdCobro { get; set; }
        public string NumeroLote { get; set; }
        public DateTime FechaProcesamiento { get; set; }
        public string MensajeError { get; set; }
    }
}
