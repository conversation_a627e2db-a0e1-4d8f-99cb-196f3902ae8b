﻿using DLWSTransacLinea.Comunes;
using DLWSTransacLinea.Reef.Api;
using DLWSTransacLinea.Structures;
using DLWSTransacLinea.Structures.POS;
using MySqlConnector;
using Newtonsoft.Json;
using Oracle.DataAccess.Client;
using Oracle.DataAccess.Types;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.ServiceModel;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using System.Xml.Serialization;

namespace DLWSTransacLinea.Cores
{
    public class AcselDB
    {
        #region BUSQUEDA
        public string buscarPolizaAcsel(string datos)
        {
            //datos = "<xmlBusqueda><CODPOL>AUPV</CODPOL><NUMPOL>29090</NUMPOL><NUMCERT/><NUMID/><DVID/><IDEPOL/></xmlBusqueda>";
            string resultado = string.Empty;
            DataTable datosObtenidos = new DataTable("Respuesta");

            XmlSerializer serializer = new XmlSerializer(typeof(xmlBusqueda));
            xmlBusqueda datosBusqueda;

            using (StringReader reader = new StringReader(datos))
            {
                datosBusqueda = (xmlBusqueda)serializer.Deserialize(reader);
            }

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();

            try
            {
                OracleCommand cmd = new OracleCommand();
                conexion = clConexion.abrirConexionOracleAcsel();
                cmd.Connection = conexion;
                cmd.CommandText = "pk_cotizador_web.Obtener_Poliza";
                cmd.CommandType = CommandType.StoredProcedure;
                OracleParameter p1 = new OracleParameter("pCodPol", OracleDbType.Varchar2);
                OracleParameter p2 = new OracleParameter("nNumPol", OracleDbType.Int64);
                OracleParameter p4 = new OracleParameter("Return_Value", OracleDbType.RefCursor);

                p1.Value = datosBusqueda.CODPOL;
                p2.Value = Int64.Parse(datosBusqueda.NUMPOL);
                p4.Direction = ParameterDirection.ReturnValue;

                cmd.Parameters.Add(p4);
                cmd.Parameters.Add(p1);
                cmd.Parameters.Add(p2);

                cmd.ExecuteNonQuery();
                datosObtenidos.Load(cmd.ExecuteReader());

                DataSet dsResultado = new DataSet("Resultados");
                dsResultado.Tables.Add(datosObtenidos);

                resultado = dsResultado.GetXml();
            }
            catch (OracleException ex)
            {
                //throw ex;
            }
            finally
            {
                conexion.Close();
            }

            return resultado;
        }

        public string buscarRequerimientosAcsel(string datos)
        {
            //datos = "<xmlBusqueda><CODPOL></CODPOL><NUMPOL></NUMPOL><NUMCERT></NUMCERT><NUMID /><DVID /><IDEPOL>1390181</IDEPOL><IDEFACT /></xmlBusqueda>";
            string resultado = string.Empty;
            DataTable datosObtenidos = new DataTable();

            XmlSerializer serializer = new XmlSerializer(typeof(xmlBusqueda));
            xmlBusqueda datosBusqueda;

            using (StringReader reader = new StringReader(datos))
            {
                datosBusqueda = (xmlBusqueda)serializer.Deserialize(reader);
            }

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();

            try
            {
                OracleCommand cmd = new OracleCommand();
                conexion = clConexion.abrirConexionOracleAcsel();
                cmd.Connection = conexion;
                cmd.CommandText = "pk_cotizador_web.Obtener_Requerimientos";
                cmd.CommandType = CommandType.StoredProcedure;
                OracleParameter p1 = new OracleParameter("pIdepol", OracleDbType.Int64);
                OracleParameter p2 = new OracleParameter("pIdefact", OracleDbType.Varchar2);
                OracleParameter p4 = new OracleParameter("Return_Value", OracleDbType.RefCursor);

                if (!string.IsNullOrEmpty(datosBusqueda.IDEPOL))
                {
                    p1.Value = datosBusqueda.IDEPOL;
                }
                else
                {
                    p1.Value = null;
                }

                if (!string.IsNullOrEmpty(datosBusqueda.IDEFACT))
                {
                    p2.Value = datosBusqueda.IDEFACT;
                }
                else
                {
                    p2.Value = null;
                }

                p4.Direction = ParameterDirection.ReturnValue;

                cmd.Parameters.Add(p4);
                cmd.Parameters.Add(p1);
                cmd.Parameters.Add(p2);

                cmd.ExecuteNonQuery();

                datosObtenidos.Load(cmd.ExecuteReader());

                DataSet dsResultado = new DataSet();
                dsResultado.Tables.Add(datosObtenidos);

                resultado = dsResultado.GetXml();
            }
            catch (OracleException ex)
            {
                //throw ex;
            }
            finally
            {
                conexion.Close();
            }

            return resultado;
        }

        /// <summary>
        /// Funcion para buscar los requerimientos de acsel
        /// </summary>
        /// <param name="pIdePol"></param>
        /// <param name="pIdeFact">para enviar varios requerimientos separarlos por comas (,)</param>
        /// <returns></returns>
        public DataTable buscarRequerimientosAcsel(string pIdePol,
                                                   string pIdeFact,
                                                   string pNumcert = null)
        {
            DataTable resultados = new DataTable();

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();

            try
            {
                OracleCommand cmd = new OracleCommand();
                conexion = clConexion.abrirConexionOracleAcsel();
                cmd.Connection = conexion;
                cmd.CommandText = "pk_cotizador_web.Obtener_Requerimientos";
                cmd.CommandType = CommandType.StoredProcedure;
                OracleParameter p1 = new OracleParameter("pIdepol", OracleDbType.Int64);
                OracleParameter p2 = new OracleParameter("pIdefact", OracleDbType.Varchar2);
                OracleParameter p3 = new OracleParameter("pNumCert", OracleDbType.Varchar2);
                OracleParameter p4 = new OracleParameter("Return_Value", OracleDbType.RefCursor);

                p1.Value = pIdePol;
                p2.Value = pIdeFact;
                p3.Value = pNumcert;
                p4.Direction = ParameterDirection.ReturnValue;

                cmd.Parameters.Add(p4);
                cmd.Parameters.Add(p1);
                cmd.Parameters.Add(p2);
                cmd.Parameters.Add(p3);

                resultados.Load(cmd.ExecuteReader());
            }
            catch (OracleException ex)
            {
            }
            finally
            {
                if (conexion.State == ConnectionState.Open) conexion.Close();
                else if (conexion.State == ConnectionState.Broken) conexion.Dispose();
            }

            return resultados;
        }

        /// <summary>
        /// Funcion para buscar polizas en Tronweb y acsel
        /// </summary>
        /// <param name="pCodPol"></param>
        /// <param name="pNumPol"></param>
        /// <param name="pNumCert"></param>
        /// <param name="pTipoId"></param>
        /// <param name="pNumId"></param>
        /// <param name="pDvId"></param>
        /// <returns></returns>
        public DataTable Core_Buscar_Poliza(string pCodPol,
                                            string pNumPol,
                                            string pNumCert,
                                            string pTipoId = "NIT",
                                            string pNumId = null,
                                            string pDvId = null)
        {
            DataTable resultados = new DataTable();

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();

            try
            {

                OracleCommand cmd = new OracleCommand();
                conexion = clConexion.abrirConexionOracleAcsel();
                if (conexion.State == ConnectionState.Open)
                {

                    cmd.Connection = conexion;
                    cmd.CommandText = "PKG_TRANSAC_ONLINE.GET_POLIZA";
                    cmd.CommandType = CommandType.StoredProcedure;
                    OracleParameter p1 = new OracleParameter("pCodPol", OracleDbType.Varchar2);
                    OracleParameter p2 = new OracleParameter("pNumPol", OracleDbType.Varchar2);
                    OracleParameter p3 = new OracleParameter("pNumCert", OracleDbType.Varchar2);
                    OracleParameter p4 = new OracleParameter("pTipoId", OracleDbType.Varchar2);
                    OracleParameter p5 = new OracleParameter("pNumId", OracleDbType.Varchar2);
                    OracleParameter p6 = new OracleParameter("pDvId", OracleDbType.Varchar2);
                    OracleParameter retorno = new OracleParameter("Return_Value", OracleDbType.RefCursor);

                    p1.Value = pCodPol;
                    p2.Value = pNumPol;
                    p3.Value = pNumCert;
                    p4.Value = pTipoId;
                    p5.Value = pNumId;
                    p6.Value = pDvId;
                    retorno.Direction = ParameterDirection.ReturnValue;

                    cmd.Parameters.Add(retorno);
                    cmd.Parameters.Add(p1);
                    cmd.Parameters.Add(p2);
                    cmd.Parameters.Add(p3);
                    cmd.Parameters.Add(p4);
                    cmd.Parameters.Add(p5);
                    cmd.Parameters.Add(p6);

                    cmd.ExecuteNonQuery();

                    resultados.Load(cmd.ExecuteReader());
                }
            }
            catch (OracleException ex)
            { }
            finally
            {
                if (conexion.State == ConnectionState.Open) conexion.Close();
                else if (conexion.State == ConnectionState.Broken) conexion.Dispose();
            }

            return resultados;
        }

        public string sisRequerimiento(string requerimiento)
        {
            string respuesta = string.Empty;

            DataTable datosAcsel = new DataTable();
            DataTable datosTronWeb = new DataTable();

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();
            conexion = clConexion.abrirConexionOracleAcsel();

            string queryAcsel = "SELECT IDEFACT FROM  factura WHERE FECSTS BETWEEN  ADD_MONTHS(SYSDATE,-36) AND SYSDATE AND IDEFACT = " + requerimiento;

            OracleDataAdapter adapter = new OracleDataAdapter(queryAcsel, conexion);

            try
            {
                adapter.Fill(datosAcsel);

                if (datosAcsel.Rows.Count > 0)
                {
                    respuesta = Constants.Systems.ACSEL;
                    conexion.Close();
                }
                else
                {
                    /*
                     * consumir servicio para validar si es recibo de reef
                     * conexion = clConexion.abrirConexionOracleTRONWEB();
                    adapter = new OracleDataAdapter(queryTronWeb, conexion);
                    adapter.Fill(datosTronWeb);

                    if (datosTronWeb.Rows.Count > 0)
                    { 
                        respuesta = Constants.Systems.TRONWEB;
                        conexion.Close();
                    }*/
                }

                conexion.Close();
            }
            catch (OracleException ex)
            {
            }
            finally
            {
                if (conexion.State == ConnectionState.Open) conexion.Close();
                else if (conexion.State == ConnectionState.Broken) conexion.Dispose();
            }

            return respuesta;
        }

        #endregion

        #region LISTA_VALORES
        public string obtenerTiposDePagoAcsel()
        {
            string resultado = string.Empty;
            DataTable datos = new DataTable();

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();
            conexion = clConexion.abrirConexionOracleAcsel();

            OracleDataAdapter adapter = new OracleDataAdapter(" SELECT descrip descripcion,codlval codigo FROM LVAL " +
                                                              " WHERE TIPOLVAL = 'DOCPCLI' " +
                                                              " AND codlval != 'CTT' " +
                                                              " AND codlval != 'CTA' " +
                                                              " AND codlval != 'TCC' " +
                                                              " ORDER BY 1 ", conexion);
            try
            {
                adapter.Fill(datos);

                DataSet dsResultado = new DataSet();
                dsResultado.Tables.Add(datos);

                resultado = dsResultado.GetXml();

                conexion.Close();
            }
            catch (OracleException ex)
            {
                conexion.Close();
            }
            finally
            {
                conexion.Close();
            }

            return resultado;
        }

        public string obtenerEntidadesFinancierasAcsel()
        {
            string resultado = string.Empty;
            DataTable datos = new DataTable();

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();
            conexion = clConexion.abrirConexionOracleAcsel();

            OracleDataAdapter adapter = new OracleDataAdapter(" SELECT descrip descripcion,codlval codigo FROM LVAL " +
                                                              " WHERE TIPOLVAL = 'ENTFIN' " +
                                                              " ORDER BY 1 ", conexion);
            try
            {
                adapter.Fill(datos);

                DataSet dsResultado = new DataSet();
                dsResultado.Tables.Add(datos);

                resultado = dsResultado.GetXml();

                conexion.Close();
            }
            catch (OracleException ex)
            {
                conexion.Close();
            }
            finally
            {
                conexion.Close();
            }

            return resultado;
        }

        public string obtenerTiposDeTarjetaAcsel()
        {
            string resultado = string.Empty;
            DataTable datos = new DataTable();

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();
            conexion = clConexion.abrirConexionOracleAcsel();

            OracleDataAdapter adapter = new OracleDataAdapter(" SELECT descrip descripcion,codlval codigo FROM LVAL " +
                                                              " WHERE TIPOLVAL = 'TIPTARCR' " +
                                                              " ORDER BY 1 ", conexion);
            try
            {
                adapter.Fill(datos);

                DataSet dsResultado = new DataSet();
                dsResultado.Tables.Add(datos);

                resultado = dsResultado.GetXml();

                conexion.Close();
            }
            catch (OracleException ex)
            {
                conexion.Close();
            }
            finally
            {
                conexion.Close();
            }

            return resultado;
        }
        #endregion

        #region TRANSACCIONES
        public string cobrarPOSAcsel(string datos)
        {
            string resultado = string.Empty;
            cobroPOS datosSolicitud = new cobroPOS();

            Estructuras.Bitacora_wsTransacLinea Bitacora = new Estructuras.Bitacora_wsTransacLinea();
            Estructuras.Bitacora_DetalleWSTransacLinea Detalle_Bitacora = new Estructuras.Bitacora_DetalleWSTransacLinea();
            DLWSTransacLinea.Cores.MySQLLogger Oper = new DLWSTransacLinea.Cores.MySQLLogger();
            OperationContext context = OperationContext.Current;
            Bitacora.URL = context.Channel.LocalAddress.Uri.AbsoluteUri.ToString();
            Bitacora.PUERTO = context.Channel.LocalAddress.Uri.Port.ToString();
            Bitacora.PATH = context.Channel.LocalAddress.Uri.LocalPath.ToString();
            Bitacora.HOST = context.Channel.LocalAddress.Uri.Host.ToString();

            Bitacora.XML_IN = datos;
            Bitacora.METODO = "PAGO POS";
            Bitacora.SISTEMA = Constants.Systems.ACSEL;

            Detalle_Bitacora.PROCEDURE = "PR_TDC_SAS.DEBITO_TDC";
            Detalle_Bitacora.METODO = "cobrarPOSAcsel";
            Detalle_Bitacora.FECHA_IN = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");

            XmlSerializer serializer = new XmlSerializer(typeof(cobroPOS));

            using (StringReader reader = new StringReader(datos))
            {
                datosSolicitud = (cobroPOS)serializer.Deserialize(reader);
            }

            if (!string.IsNullOrWhiteSpace(datosSolicitud.pIdTransaccion))
            {
                Bitacora.ID_TRANSACCION = Int64.Parse(datosSolicitud.pIdTransaccion);
            }
            else
            {
                Bitacora = Oper.Insert_Bitacora(Bitacora);
            }

            OracleConnection conexion = new OracleConnection();
            try
            {
                ConexionBD clConexion = new ConexionBD();
                conexion = clConexion.abrirConexionOracleAcsel();
                OracleCommand cmd = new OracleCommand();

                cmd.Connection = conexion;
                cmd.CommandText = "PR_TDC_SAS.DEBITO_TDC";
                cmd.CommandType = CommandType.StoredProcedure;
                OracleParameter p1 = new OracleParameter("pCodProd", OracleDbType.Varchar2, 32767);
                OracleParameter p2 = new OracleParameter("pNumPol", OracleDbType.Int32);
                OracleParameter p3 = new OracleParameter("pNumCert", OracleDbType.Int32);
                OracleParameter p4 = new OracleParameter("pCodEntFinan", OracleDbType.Varchar2, 32767);
                OracleParameter p5 = new OracleParameter("pCardNumber", OracleDbType.Varchar2, 32767);
                OracleParameter p6 = new OracleParameter("pMonto", OracleDbType.Double);
                OracleParameter p7 = new OracleParameter("pFecVencTDC", OracleDbType.Varchar2, 32767);
                OracleParameter p8 = new OracleParameter("pCodUsr", OracleDbType.Varchar2, 32767);
                OracleParameter p9 = new OracleParameter("Return_Value", OracleDbType.XmlType, 32767);

                p1.Value = datosSolicitud.pCodProd;
                p2.Value = Convert.ToInt32(datosSolicitud.pNumPol);
                p3.Value = Convert.ToInt32(datosSolicitud.pNumCert);
                p4.Value = datosSolicitud.pCodEntFinan;
                p5.Value = datosSolicitud.pCardNumber;
                p6.Value = Convert.ToDouble(datosSolicitud.pMonto);
                p7.Value = "01/" + datosSolicitud.pFecVencTDC;
                p8.Value = datosSolicitud.pCodUsr;
                p9.Direction = ParameterDirection.ReturnValue;

                cmd.Parameters.Add(p9);
                cmd.Parameters.Add(p1);
                cmd.Parameters.Add(p2);
                cmd.Parameters.Add(p3);
                cmd.Parameters.Add(p4);
                cmd.Parameters.Add(p5);
                cmd.Parameters.Add(p6);
                cmd.Parameters.Add(p7);
                cmd.Parameters.Add(p8);

                cmd.ExecuteNonQuery();
                resultado = ((OracleXmlType)cmd.Parameters["Return_Value"].Value).Value;

                DEBITO_SAS vDEBITO = new DEBITO_SAS();
                XmlSerializer serializer_ = new XmlSerializer(typeof(DEBITO_SAS), new XmlRootAttribute("DEBITO_SAS"));

                using (StringReader reader = new StringReader(resultado))
                {
                    vDEBITO = (DEBITO_SAS)serializer_.Deserialize(reader);
                }

                vDEBITO.DATOS.IDTransaccion_ref = Bitacora.ID_TRANSACCION.ToString();

                if (vDEBITO.DATOS.CodRespuesta != "00")
                {
                    Detalle_Bitacora.ERROR = "1";
                    Detalle_Bitacora.COBRO_TARJETA = "0";
                    Detalle_Bitacora.MENSAJE = vDEBITO.DATOS.DescRespuesta;
                    Bitacora.MENSAJE = "ERROR DE COBRO";
                    Bitacora.ERROR = "1";
                    Bitacora.MONEDA = datosSolicitud.pCodProd.Substring(datosSolicitud.pCodProd.Length - 1); ;
                    Bitacora.TOTAL = datosSolicitud.pMonto;
                }
                else
                {
                    Detalle_Bitacora.ERROR = "0";
                    Detalle_Bitacora.COBRO_TARJETA = "1";
                    Bitacora.MENSAJE = "COBRADO EXITOSAMENTE";
                    Bitacora.ERROR = "0";
                    Bitacora.MONEDA = datosSolicitud.pCodProd.Substring(datosSolicitud.pCodProd.Length - 1); ;
                    Bitacora.TOTAL = datosSolicitud.pMonto;
                }

                var doc = new XmlDocument();
                doc.LoadXml(resultado);

                XmlNode newElem = doc.CreateNode("element", "ID_TRANSACCION", "");
                newElem.InnerText = Bitacora.ID_TRANSACCION.ToString();
                XmlElement root = doc.DocumentElement;
                root.AppendChild(newElem);

                using (StringWriter sw = new StringWriter())
                {
                    using (XmlTextWriter tx = new XmlTextWriter(sw))
                    {
                        doc.WriteTo(tx);
                        resultado = sw.ToString();
                    }
                }

                Bitacora.XML_OUT = resultado.ToString();
                //ACTUALIZAR ENCABEZADO BITACORA
                Oper.Update_Bitacora(Bitacora);

                Detalle_Bitacora.XML_OUT = resultado.ToString();
                Detalle_Bitacora.ID_TRANSACCION = Bitacora.ID_TRANSACCION;
                Detalle_Bitacora.FECHA_OUT = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
                Detalle_Bitacora.EXTRA_DATA = datosSolicitud.pRecibos;
                Bitacora.Detalle_Bitacora = Detalle_Bitacora;
                //INSERTAR DETALLE BITACORA
                Oper.Insert_DetalleBitacora(Bitacora);

            }
            catch (OracleException ex)
            {
                resultado = ex.Message;
                conexion.Close();

                Detalle_Bitacora.ERROR = "1";
                Detalle_Bitacora.COBRO_TARJETA = "0";
                Detalle_Bitacora.MENSAJE = ex.Message.ToString();
                Detalle_Bitacora.FECHA_OUT = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
                Detalle_Bitacora.EXTRA_DATA = datosSolicitud.pRecibos;
                Bitacora.Detalle_Bitacora = Detalle_Bitacora;
                Oper.Insert_DetalleBitacora(Bitacora);
            }
            finally
            {
                conexion.Close();
            }

            return resultado;
        }

        /// <summary>
        /// FUNCION PARA RETORNAR DINERO DE TRANSACCION POS
        /// </summary>
        /// <param name="datos"></param>
        /// <returns></returns>
        public string reversarPOS(string datos, string pIdTransaccion = null)
        {
            string resultado = string.Empty;

            respuestaPOS clRespuestaPOS = new respuestaPOS();
            XmlSerializer serializer_ = new XmlSerializer(typeof(respuestaPOS));

            using (StringReader reader = new StringReader(datos))
            {
                clRespuestaPOS = (respuestaPOS)serializer_.Deserialize(reader);
            }

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();

            try
            {
                OracleCommand cmd = new OracleCommand();
                conexion = clConexion.abrirConexionOracleAcsel();
                cmd.Connection = conexion;
                cmd.CommandText = "pr_tdc_sas.cancel_transac";
                cmd.CommandType = CommandType.StoredProcedure;
                OracleParameter p1 = new OracleParameter("pIDTerminal", OracleDbType.Varchar2, 32767);
                OracleParameter p2 = new OracleParameter("pReferencia", OracleDbType.Varchar2, 32767);
                OracleParameter p3 = new OracleParameter("pAutorizacion", OracleDbType.Varchar2, 32767);
                OracleParameter p4 = new OracleParameter("pSystemTraceID", OracleDbType.Varchar2, 32767);
                OracleParameter p5 = new OracleParameter("pCodUsr", OracleDbType.Varchar2, 32767);
                OracleParameter p6 = new OracleParameter("Return_Value", OracleDbType.Varchar2, 32767);

                p1.Value = clRespuestaPOS.IdTerminal;
                p2.Value = clRespuestaPOS.NumReferencia;
                p3.Value = clRespuestaPOS.NumAutorizacion;
                p4.Value = clRespuestaPOS.IDTransaccion;
                p5.Value = clRespuestaPOS.pCodUsr;
                p6.Direction = ParameterDirection.ReturnValue;

                cmd.Parameters.Add(p6);
                cmd.Parameters.Add(p1);
                cmd.Parameters.Add(p2);
                cmd.Parameters.Add(p3);
                cmd.Parameters.Add(p4);
                cmd.Parameters.Add(p5);

                cmd.ExecuteNonQuery();

                resultado = cmd.Parameters["Return_Value"].Value.ToString();

                conexion.Close();
            }
            catch (Exception ex)
            {
                conexion.Close();
                resultado = "ERROR " + ex.Message;
            }
            finally
            {
                conexion.Close();
            }

            return resultado;
        }

        public DataSet pagarAcsel(string xmlCobro, Estructuras.Bitacora_wsTransacLinea Bitacora = null)
        {
            Estructuras clEstructuras = new Estructuras();
            DataSet dtsRespuesta = new DataSet();


            string respuesta = string.Empty;

            //xmlCobro = "<PAGO><REQUERIMIENTOS><REQUERIMIENTO>25072007</REQUERIMIENTO></REQUERIMIENTOS><TIPO_PAGO><TIPO><CODIGO>EFE</CODIGO><MONEDA>Q</MONEDA><MONTO>279.37</MONTO><CODENTFINAN/><NUMREFDOC/><NUMAUTORIZA/></TIPO></TIPO_PAGO></PAGO>";
            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();

            Estructuras.Bitacora_DetalleWSTransacLinea Detalle_Bitacora = new Estructuras.Bitacora_DetalleWSTransacLinea();
            DLWSTransacLinea.Cores.MySQLLogger Oper = new DLWSTransacLinea.Cores.MySQLLogger();
            Detalle_Bitacora.PROCEDURE = "PK_COTIZADOR_WEB.COBRAR";
            Detalle_Bitacora.METODO = "pagarAcsel";
            Detalle_Bitacora.XML_IN = xmlCobro;
            Detalle_Bitacora.FECHA_IN = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");

            try
            {
                OracleCommand cmd = new OracleCommand();
                conexion = clConexion.abrirConexionOracleAcsel();
                cmd.Connection = conexion;
                cmd.CommandText = "pk_cotizador_web.cobrar";
                cmd.CommandType = CommandType.StoredProcedure;
                OracleParameter p1 = new OracleParameter("xmlPago", OracleDbType.XmlType);
                OracleParameter p2 = new OracleParameter("Return_Value", OracleDbType.XmlType);

                p1.Value = xmlCobro;
                p2.Direction = ParameterDirection.ReturnValue;

                cmd.Parameters.Add(p2);
                cmd.Parameters.Add(p1);

                cmd.ExecuteNonQuery();

                respuesta = (((OracleXmlType)cmd.Parameters["Return_Value"].Value)).Value;

                conexion.Close();

                dtsRespuesta = clEstructuras.XML_DataSet(respuesta);

                if (dtsRespuesta.Tables.Count > 0)
                {
                    if (dtsRespuesta.Tables[0].Rows[0]["Codigo"].ToString() == "1")
                    {
                        Detalle_Bitacora.ERROR = "0";
                        Detalle_Bitacora.PAGAR_RECIBO = "1";
                    }
                    else
                    {
                        Detalle_Bitacora.ERROR = "1";
                        Detalle_Bitacora.PAGAR_RECIBO = "0";
                    }

                    Detalle_Bitacora.ID_TRANSACCION = Bitacora.ID_TRANSACCION;
                    Detalle_Bitacora.XML_OUT = respuesta.ToString();
                    Detalle_Bitacora.FECHA_OUT = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
                    Bitacora.Detalle_Bitacora = Detalle_Bitacora;
                    Oper.Insert_DetalleBitacora(Bitacora);
                }
            }
            catch (OracleException ex)
            {
                conexion.Close();
                respuesta = "ERROR " + ex.Message;
                Detalle_Bitacora.PAGAR_RECIBO = "0";
                Detalle_Bitacora.ERROR = "1";
                Detalle_Bitacora.MENSAJE = ex.Message.ToString();
                Detalle_Bitacora.FECHA_OUT = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
                Bitacora.Detalle_Bitacora = Detalle_Bitacora;
                Oper.Insert_DetalleBitacora(Bitacora);
            }
            finally
            {
                conexion.Close();
            }

            return dtsRespuesta;
        }
        #endregion

        #region FACTURA
        public string obtenerReporteFacturasAcsel(string requerimientos)
        {
            //requerimientos = "<xmlRequerimientos><Requerimiento>12855697</Requerimiento></xmlRequerimientos>";
            XmlSerializer serializer = new XmlSerializer(typeof(xmlRequerimientos));
            xmlRequerimientos datosSolicitud = new xmlRequerimientos();

            using (StringReader reader = new StringReader(requerimientos))
            {
                datosSolicitud = (xmlRequerimientos)serializer.Deserialize(reader);
            }
            string resultado = string.Empty;

            if (datosSolicitud.requerimientos.Count > 0)
            {
                string listadoReqs = generarListadoFacturas(datosSolicitud.requerimientos);

                string query = "SELECT F.IDEFACT IDEFACT, (F.CODPOL || '-' || F.NUMPOL) POLIZA, " +
                            "   TO_CHAR(TRUNC(DF.FECMOV),'DD/MM/RRRR') FECHA_COBRO, NVL(F.CODFACT, '**') SERIE, " +
                            "   F.NUMFACT FACTURA, F.MTOFACTLOCAL TOTAL_DOLAR, F.MTOFACTMONEDA PRIMATOTAL, " +
                            "   PR_FACTURA.DETALLE_FACTURA(F.IDEFACT,'F') CONCEPTO, PR_MANTENIMIENTO.NOMBRE_AGENTE(F.CODINTER) NOMBRE_AGENTE, " +
                            "   F.CAE CAE, F.CODMONEDA COD_MONEDA, F.CODFACT CODFACT, F.NUMFACT NUMFACT, CF.NUMFACTFIN NUMFACTFIN, " +
                            "   CF.NORESOLUCION NORESOLUCION, TO_CHAR(CF.FECRESOLUCION,'DD/MM/RRRR') FECRESOLUCION, " +
                            "   CF.IMPRENTA IMPRENTA, CF.NITIMPRENTA NITIMPRENTA, CF.FACE FACE, CF.DISPOSITIVO DISPOSITIVO, " +
                            "   NVL(PR_FACTURA.DATOS_FACTURA_GFACE(F.IDEFACT, 'F', F.CODFACT, F.NUMFACT, 'D'), PR_MANTENIMIENTO.TERCERO_NOM_O_DIREC(F.TIPOID,F.NUMID,F.DVID,'D')) DIRECCIONCLIENTE, " +
                            "   PR_MANTENIMIENTO.RETORNA_NIT(F.TIPOID,F.NUMID,F.DVID) NIT, NVL(PR_FACTURA.DATOS_FACTURA_GFACE(F.IDEFACT, 'F', F.CODFACT, F.NUMFACT, 'N'), PR_MANTENIMIENTO.TERCERO_NOM_O_DIREC(F.TIPOID,F.NUMID,F.DVID,'N')) NOMBRE, " +
                            "   CF.PERIODO PERIODO, " +
                            "   UPPER(PR_MONTO_ESCRITO.MTO_ESCRITO_MONEDA(ROUND(F.MTOFACTLOCAL,2),F.CODMONEDA)) TOTAL_LETRAS, " +
                            "   cf.numfact numfactini, SUBSTR(TO_CHAR( periodo),3,2) || lpad(f.NUMFACT,10, '0') numero, " +
                            "   NVL(PR_FACTURA.DATOS_FACTURA_GFACE(F.IDEFACT, 'F', F.CODFACT, F.NUMFACT, 'T'), 1) TASA, " +
                            "   UPPER (E.direc) DIREC, (P.DESCPAIS || ', ' || D.DESCESTADO) LUGAR, " +
                            "   ( 'Pais ' || ACSEL.PR.BUSCA_LVAL ('ISOPAIS', E.CODPAIS) || ', ' || 'C.P. ' || E.CODIGO_POSTAL) CODIGO, " +
                            "   'NIT: 8466114' NIT_EST " +
                            "   FROM FACTURA F, CORRELATIVO_FACTURA CF, REL_ING RI, DOCUMENTO_FISCAL DF, ESTABLECIMIENTO E, " +
                            "   PAIS P, ESTADO D, CIUDAD C WHERE F.CODFACT = CF.CODFACT AND F.NUMRELING = RI.NUMRELING " +
                            "   AND F.CODFACT   = DF.CODFACT AND F.NUMFACT   = DF.NUMFACT AND DF.INDTIPO  = 'F' AND CF.FACE IS NOT NULL " +
                            "   AND f.numfact BETWEEN cf.numfact AND cf.numfactfin " +
                            "   AND E.CODCIA          = CF.CODCIA AND E.ESTABLECIMIENTO = CF.ESTABLECIMIENTO AND P.CODPAIS = E.CODPAIS " +
                            "   AND D.CODPAIS         = E.CODPAIS AND D.CODESTADO       = E.CODESTADO AND C.CODPAIS         = E.CODPAIS" +
                            "   AND C.CODESTADO       = E.CODESTADO AND C.CODCIUDAD       = E.CODCIUDAD " +
                            "   AND F.IDEFACT        IN (" + listadoReqs + ") " +
                            "   UNION ALL " +
                            "   SELECT NUM_RECIBO IDEFACT, TO_CHAR(NUM_POLIZA) POLIZA, TO_CHAR(TRUNC(FECHA_COBRO),'DD/MM/RRRR') FECHA_COBRO, " +
                            "   NVL(SERIE, '**') SERIE,  TO_NUMBER(NUMFACT) FACTURA, MTOFACTLOCAL TOTAL_DOLAR, " +
                            "   MTOFACTMONEDA PRIMATOTAL, CONCEPTO, NOMBRE_AGENTE, CAE, CODMONEDA, SERIE CODFACT, TO_NUMBER(NUMFACT), " +
                            " NUMFACTFIN, NORESOLUCION, TO_CHAR(FEC_RESOLUCION,'DD/MM/RRRR') FEC_RESOLUCION, " +
                            " NOM_IMPRENTA IMPRENTA, NIT_IMPRENTA NITIMPRENTA, COD_FACE FACE, COD_DISPOSITIVO DISPOSITIVO, " +
                            " DIRECCION DIRECCIONCLIENTE, NIT, " +
                            " NOMBRE_FACTURA NOMBRE, TO_NUMBER(COD_PERIODO) PERIODO, TOTAL_LETRAS, numfactini, " +
                            " SUBSTR(TO_CHAR(COD_PERIODO),3,2) ||lpad(NUMFACT,10, '0') numero, TO_CHAR(1) TASA, " +
                            " 'AVENIDA REFORMA  9-55 ZONA 10 EDIFICIO REFORMA 10 NIVEL 3 OFICINA  304' DIREC, " +
                            " 'GUATEMALA, GUATEMALA' LUGAR, 'Pais GT, C.P. 01010' CODIGO, 'NIT: 8466114' NIT_EST " +
                            " FROM VW_FACTURAS@DBLTRONWEB " +
                            "   WHERE NUM_RECIBO IN(" + listadoReqs + ") ";

                DataTable datos = new DataTable();

                OracleConnection conexion = new OracleConnection();
                ConexionBD clConexion = new ConexionBD();
                conexion = clConexion.abrirConexionOracleAcsel();

                OracleDataAdapter adapter = new OracleDataAdapter(query, conexion);

                try
                {
                    adapter.Fill(datos);

                    DataSet dsResultado = new DataSet();
                    dsResultado.Tables.Add(datos);

                    resultado = dsResultado.GetXml();

                    conexion.Close();
                }
                catch (OracleException ex)
                {
                    conexion.Close();
                    //throw ex;
                }
                finally
                {
                    conexion.Close();
                }
            }

            return resultado;
        }

        public string obtenerReporteFacturasFel(string requerimientos)
        {
            string vRespuesta = string.Empty;
            byte[] Buffer = null;
            ConexionBD clsConexion = new ConexionBD();
            OracleBlob blob;

            using (OracleConnection conexionOracle = clsConexion.abrirConexionOracleAcsel())
            {
                try
                {
                    OracleCommand cmd = new OracleCommand();
                    cmd.Connection = conexionOracle;
                    cmd.CommandText = "ACSEL.PR_DOC_FEL.FNC_FEL_IDEFACT";
                    cmd.CommandType = CommandType.StoredProcedure;

                    OracleParameter p1 = new OracleParameter("inIdeFact", OracleDbType.Varchar2, 50000);
                    OracleParameter retorno = new OracleParameter("Return_Value", OracleDbType.Blob);

                    p1.Value = requerimientos;
                    retorno.Direction = ParameterDirection.ReturnValue;

                    cmd.Parameters.Add(retorno);
                    cmd.Parameters.Add(p1);

                    cmd.ExecuteNonQuery();

                    blob = (OracleBlob)cmd.Parameters["Return_Value"].Value;
                    Buffer = new byte[blob.Length];
                    blob.Read(Buffer, 0, (int)blob.Length);

                    vRespuesta = Convert.ToBase64String(Buffer, 0, Buffer.Length);

                    cmd.Dispose();
                    cmd = null;
                    blob.Close();
                    blob.Dispose();
                    blob = null;
                }
                catch (Exception e)
                {
                    //Lbl_Cotizado_Autos_Web.Logs.log_error.getException(e);
                }
                finally
                {
                    conexionOracle.Close();
                }
            }

            return vRespuesta;
        }

        private string generarListadoFacturas(List<xmlRequerimientos.requerimiento> facturas)
        {
            string resultado = string.Empty;
            for (int i = 0; i < facturas.Count(); i++)
            {
                if (i == facturas.Count() - 1)
                {
                    resultado += facturas[i].req;
                }
                else
                {
                    resultado += facturas[i].req + ",";
                }
            }
            return resultado;
        }
        #endregion

        #region RECIBO
        public string obtenerInfoRecibo(string pRequerimiento)
        {
            string resultado = string.Empty;
            DataTable datos = new DataTable();

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();
            conexion = clConexion.abrirConexionOracleAcsel();

            #region QUERY
            string query = "SELECT " +
                            "  FAC.IDEFACT NO_REQUERIMIENTO, " +
                            "  NVL(PR_FACTURA.DATOS_FACTURA_GFACE(FAC.IDEFACT, 'F', FAC.CODFACT, FAC.NUMFACT, 'N'), PR_MANTENIMIENTO.TERCERO_NOM_O_DIREC(FAC.TIPOID,FAC.NUMID,FAC.DVID,'N')) NOMBRE, " +
                            "  FAC.NUMID||'-'||FAC.DVID NIT, " +
                            "  TO_CHAR(NVL((SELECT " +
                            "                RI.FECSTSRELING " +
                            "              FROM " +
                            "                REL_ING RI " +
                            "              WHERE " +
                            "                RI.NUMRELING = FAC.NUMRELING " +
                            "            ),SYSDATE),'DD/MM/RRRR')FECHA, " +
                            "  '' POLIZA, " +
                            "  '' VIGENCIA, " +
                            "  PR_FACTURA.DETALLE_FACTURA(FAC.IDEFACT,'F') CONCEPTO, " +
                            "  PR_MANTENIMIENTO.NOMBRE_AGENTE(FAC.CODINTER) NOMBRE_AGENTE, " +
                            "  NVL(PR_FACTURA.DATOS_FACTURA_GFACE(FAC.IDEFACT, 'F', FAC.CODFACT, FAC.NUMFACT, 'D'), PR_MANTENIMIENTO.TERCERO_NOM_O_DIREC(FAC.TIPOID,FAC.NUMID,FAC.DVID,'D')) DIRECCION_COBRO, " +
                            "  ACRE.CODMONEDA MONEDA, " +
                            "  TO_CHAR(ACRE.MTOACRELOCAL,'999,999,999,999.99') TOTAL " +
                            "FROM " +
                            "  FACTURA FAC, " +
                            "  ACREENCIA ACRE " +
                            "WHERE " +
                            "  ACRE.IDEFACT = FAC.IDEFACT " +
                            "  AND FAC.IDEFACT = " + pRequerimiento + " ";
            #endregion

            OracleDataAdapter adapter = new OracleDataAdapter(query, conexion);
            try
            {
                adapter.Fill(datos);

                DataSet dsResultado = new DataSet();
                dsResultado.Tables.Add(datos);
                resultado = dsResultado.GetXml();
                conexion.Close();
            }
            catch (OracleException ex)
            {
                conexion.Close();
            }
            finally
            {
                conexion.Close();
            }

            return resultado;
        }

        public string obtenerDetalleRecibo(string pRequerimiento)
        {

            string resultado = string.Empty;
            DataTable datos = new DataTable();

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();
            conexion = clConexion.abrirConexionOracleAcsel();

            #region QUERY
            string query = "SELECT " +
                            "  CA.DESCCPTOACRE CONCEPTO, " +
                            "  TO_CHAR(DA.MTODETACREMONEDA,'999,999,999,999.99') MONTO, " +
                            "  DA.CODMONEDA MONEDA, " +
                            "  TO_CHAR(DA.MTODETACRELOCAL,'999,999,999,999.99') TOTAL " +
                            "FROM " +
                            "  DET_ACRE DA, " +
                            "  CPTO_ACRE CA " +
                            "WHERE " +
                            "  DA.NUMACRE = ( SELECT NUMACRE FROM ACREENCIA WHERE IDEFACT = " + pRequerimiento + ") " +
                            "AND DA.CODGRUPOACRE = CA.CODGRUPOACRE " +
                            "AND DA.CODCPTOACRE  = CA.CODCPTOACRE " +
                            "ORDER BY " +
                            "  NUMDETACRE";
            #endregion

            OracleDataAdapter adapter = new OracleDataAdapter(query, conexion);
            try
            {
                adapter.Fill(datos);

                DataSet dsResultado = new DataSet();
                dsResultado.Tables.Add(datos);
                resultado = dsResultado.GetXml();
                conexion.Close();
            }
            catch (OracleException ex)
            {
                conexion.Close();
            }
            finally
            {
                conexion.Close();
            }

            return resultado;
        }
        /// <summary>
        /// Recupera el requerimiento de acsel equivalente al recibo de tw
        /// </summary>
        /// <param name="pReciboTron"></param>
        /// <returns></returns>
        public Tuple<string, string> get_factura_acsel_migrada(string pReciboTron)
        {
            /*
             * PENDIENTE DE VALIDAR SI HABRAN RECIBOS MIG de ACSEL en REEF
             * string v_num_requerimiento_acsel = string.Empty;
            string v_num_codfact_acsel = string.Empty;

            try
            {
                string v_num_poliza_tron = string.Empty;
                string v_num_cuota_tron = string.Empty;

                DataTable dtInfoPolizaCuota = new DataTable();
                DataTable dtInfoRequerimiento = new DataTable();

                dtInfoPolizaCuota = get_poliza_cuota_recibo_tw(pReciboTron);

                if (dtInfoPolizaCuota.Rows.Count > 0)
                {
                    v_num_poliza_tron = dtInfoPolizaCuota.Rows[0]["NUM_POLIZA"].ToString();
                    v_num_cuota_tron = dtInfoPolizaCuota.Rows[0]["NUM_CUOTA"].ToString();
                    //
                    dtInfoRequerimiento = get_factura_acsel_mig_t(v_num_poliza_tron, v_num_cuota_tron);
                    if (dtInfoRequerimiento.Rows.Count > 0)
                    {
                        v_num_requerimiento_acsel = dtInfoRequerimiento.Rows[0]["idefact"].ToString();
                        v_num_codfact_acsel = dtInfoRequerimiento.Rows[0]["codfact"].ToString();
                    }
                }
            }
            catch (Exception ex)
            {
            }*/

            return new Tuple<string, string>(string.Empty, string.Empty);
        }
        #endregion

        #region TRANSACCION
        /// <summary>
        /// Proceso que asigna el correo de notificacion
        /// </summary>
        /// <param name="pIdTransaccion"></param>
        /// <param name="pEmail"></param>
        /// <returns></returns>
        public string Transact_Set_Email_Noti(string pIdTransaccion, string pEmail)
        {
            string vRespuesta = string.Empty;
            ConexionBD clConexiones = new ConexionBD();
            MySqlConnection conexion = new MySqlConnection();
            conexion = clConexiones.abrirConexionMysqlLog();
            MySqlParameter resultado = new MySqlParameter();

            MySqlCommand cmd = new MySqlCommand("fnc_Asignar_Email_Noti", conexion);

            try
            {
                resultado.Direction = System.Data.ParameterDirection.ReturnValue;
                cmd.CommandType = CommandType.StoredProcedure;

                cmd.Parameters.AddWithValue("@PIDTRANSACCION", pIdTransaccion);
                cmd.Parameters.AddWithValue("@PEMAIL", pEmail);

                cmd.Parameters.Add(resultado);
                cmd.ExecuteNonQuery();

                vRespuesta = resultado.Value.ToString();
            }
            catch (Exception ex)
            {
                vRespuesta = "-1";
            }
            finally
            {
                conexion.Close();
                conexion.Dispose();
            }

            return vRespuesta;
        }
        #endregion
    }
}
