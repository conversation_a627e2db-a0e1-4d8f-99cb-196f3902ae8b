using System;
using System.Collections.Generic;

namespace DLWSTransacLinea.Reef.Structures
{
    /// <summary>
    /// Estructura para el procesamiento de cobranza masiva
    /// </summary>
    public class CollectMassiveRequest
    {
        /// <summary>
        /// Lista de encabezados de recibos para cobranza masiva
        /// </summary>
        public List<ReceiptMassiveHeader> oRcpMtgHedPT { get; set; }

        /// <summary>
        /// Lista de recibos para cobranza masiva
        /// </summary>
        public List<ReceiptMassiveItem> oRcpMtgPT { get; set; }

        /// <summary>
        /// Lista de costos para cobranza masiva
        /// </summary>
        public List<ReceiptMassiveCost> oRcpMtgCosPT { get; set; }

        public CollectMassiveRequest()
        {
            oRcpMtgHedPT = new List<ReceiptMassiveHeader>();
            oRcpMtgPT = new List<ReceiptMassiveItem>();
            oRcpMtgCosPT = new List<ReceiptMassiveCost>();
        }
    }

    /// <summary>
    /// Encabezado de recibo para cobranza masiva
    /// </summary>
    public class ReceiptMassiveHeader
    {
        /// <summary>
        /// Tipo de banco de cheque
        /// </summary>
        public string chtBnkTypVal { get; set; }

        /// <summary>
        /// Tipo de pago de cobranza
        /// </summary>
        public string cloPymTypVal { get; set; }

        /// <summary>
        /// Tipo de situación de cobranza
        /// </summary>
        public string cloStsTypVal { get; set; }

        /// <summary>
        /// Compañía
        /// </summary>
        public int cmpVal { get; set; }

        /// <summary>
        /// Valor de identificación de costo
        /// </summary>
        public int cosIdnVal { get; set; }

        /// <summary>
        /// Valor de moneda de costo
        /// </summary>
        public int cosCrnVal { get; set; }

        /// <summary>
        /// Valor de moneda
        /// </summary>
        public int crnVal { get; set; }

        /// <summary>
        /// Cajero
        /// </summary>
        public string cshVal { get; set; }

        /// <summary>
        /// Valor de método
        /// </summary>
        public int mtcVal { get; set; }

        /// <summary>
        /// Observaciones
        /// </summary>
        public string obsVal { get; set; }

        /// <summary>
        /// Texto del gateway de pago
        /// </summary>
        public string pgwTxtVal { get; set; }

        /// <summary>
        /// Gateway de pago
        /// </summary>
        public string pgwVal { get; set; }

        /// <summary>
        /// Fecha de procesamiento
        /// </summary>
        public string pocDat { get; set; }

        /// <summary>
        /// Valor de término de proceso
        /// </summary>
        public string prcTrmVal { get; set; }

        /// <summary>
        /// Monto del recibo
        /// </summary>
        public decimal rcpAmn { get; set; }

        /// <summary>
        /// Valor del recibo
        /// </summary>
        public int rcpVal { get; set; }

        /// <summary>
        /// Tipo de cuenta simplificada
        /// </summary>
        public string smfAcoTypVal { get; set; }

        /// <summary>
        /// Valor de cuenta simplificada
        /// </summary>
        public string smfAcoVal { get; set; }

        /// <summary>
        /// Fecha de envío
        /// </summary>
        public string sndDat { get; set; }

        /// <summary>
        /// Usuario
        /// </summary>
        public string usrVal { get; set; }
    }

    /// <summary>
    /// Item de recibo para cobranza masiva
    /// </summary>
    public class ReceiptMassiveItem : ReceiptMassiveHeader
    {
        // Hereda todas las propiedades de ReceiptMassiveHeader
        // Se mantiene como clase separada para claridad en la estructura del JSON
    }

    /// <summary>
    /// Costo de recibo para cobranza masiva
    /// </summary>
    public class ReceiptMassiveCost : ReceiptMassiveHeader
    {
        // Hereda todas las propiedades de ReceiptMassiveHeader
        // Se mantiene como clase separada para claridad en la estructura del JSON
    }
}
