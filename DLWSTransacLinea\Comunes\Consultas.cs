﻿using DLWSTransacLinea.Reef.Api;
using DLWSTransacLinea.Reef.BusinessLogic;
using DLWSTransacLinea.Reef.Services;
using DLWSTransacLinea.Reef.Models;
using DLWSTransacLinea.Comunes;
using MySqlConnector;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Oracle.DataAccess.Client;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Net;
using System.ServiceModel;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Linq;
using DLWSTransacLinea.Structures;
using DLWSTransacLinea.Cores;

namespace DLWSTransacLinea.Comunes
{
    public class Consultas
    {
        #region Public Methods

        /// <summary>
        /// Proceso para ejecutar las consultas
        /// </summary>
        /// <param name="xmlSolicitud"></param>
        /// <returns></returns>
        public XElement ConsultarTransaccion(xmlTransaccion._transaccion xmlSolicitud)
        {
            XElement resultado = null;
            try
            {
                xmlTransaccion clRespuesta = new xmlTransaccion();

                DLWSTransacLinea.Cores.MySQLLogger Oper = new Cores.MySQLLogger();

                #region INIZIALIZACION

                xmlTransaccion._transaccion clTransaccion = new xmlTransaccion._transaccion();
                clTransaccion.datos = new xmlTransaccion._estructuraTransaccion();
                clTransaccion.datos.encabezado = new xmlTransaccion._encabezado();
                clTransaccion.datos.polizas = new xmlTransaccion._poliza();
                clTransaccion.datos.polizas.poliza = new List<xmlTransaccion.poliza>();
                clTransaccion.datos.requerimientos = new xmlTransaccion._requerimiento();
                clTransaccion.datos.requerimientos.requerimiento = new List<xmlTransaccion.requerimiento>();
                clTransaccion.datos.identificadores = new xmlTransaccion._identificadores();
                clTransaccion.datos.mediosPago = new xmlTransaccion._medio();
                clTransaccion.datos.mediosPago.medio = new List<xmlTransaccion.medio>();

                #endregion INIZIALIZACION

                #region ASIGNACION

                clTransaccion.datos.encabezado = xmlSolicitud.datos.encabezado;
                clTransaccion.datos.identificadores = xmlSolicitud.datos.identificadores;

                #endregion ASIGNACION

                Cores.AcselDB acselDB = new Cores.AcselDB();
                TronwebDB tronwebDB = new TronwebDB();

                #region TIPO | BUSQUEDA

                switch (xmlSolicitud.datos.encabezado.tipoBusqueda)
                {
                    case "REQUERIMIENTO":
                        //BUSQUEDA DE REQUERIMIENTOS POR POLIZA/IDEPOL
                        if (!string.IsNullOrWhiteSpace(xmlSolicitud.datos.identificadores.idepol))
                        {
                            Cores.AcselDB clOperacion = new Cores.AcselDB();
                            DataTable dtInfoRequerimientos = new DataTable();

                            #region Consulta|Requerimientos

                            switch (xmlSolicitud.datos.identificadores.sistema)
                            {
                                case "A":
                                    dtInfoRequerimientos = clOperacion.buscarRequerimientosAcsel(xmlSolicitud.datos.identificadores.idepol,
                                                                                                 (!string.IsNullOrWhiteSpace(xmlSolicitud.datos.identificadores.requerimiento)
                                                                                                 ? xmlSolicitud.datos.identificadores.requerimiento.Replace(";", ",") : null),
                                                                                                 xmlSolicitud.datos.identificadores.numcert);
                                    break;
                                case "T":
                                    dtInfoRequerimientos = tronwebDB.BuscarRequerimientos_TW(pNumPol: xmlSolicitud.datos.identificadores.idepol,
                                                                                             pVigenciaIni: xmlSolicitud.datos.identificadores.vigencia_inicial,
                                                                                             pPolizaGrupo: xmlSolicitud.datos.identificadores.es_poliza_grupo,
                                                                                             pTipDocum: xmlSolicitud.datos.identificadores.tip_docum,
                                                                                             pCodDocum: xmlSolicitud.datos.identificadores.cod_docum);
                                    break;
                                case "R":
                                    ConsultasReef consultasReef = new ConsultasReef();
                                    dtInfoRequerimientos = consultasReef.ObtenerDocumentoPago(policyNumber: xmlSolicitud.datos.identificadores.idepol.ToString(),
                                                                                              initialValidity: xmlSolicitud.datos.identificadores.vigencia_inicial,
                                                                                              isGroupPolicy: xmlSolicitud.datos.identificadores.es_poliza_grupo,
                                                                                              thirdPartyDocumentType: xmlSolicitud.datos.identificadores.tip_docum,
                                                                                              thirdPartyDocumentCode: xmlSolicitud.datos.identificadores.cod_docum);
                                    break;

                                default:
                                    //UNION
                                    break;
                            }

                            #endregion Consulta|Requerimientos

                            #region Asignacion
                            if (dtInfoRequerimientos.Rows.Count > 0)
                            {
                                for (int i = 0; i < dtInfoRequerimientos.Rows.Count; i++)
                                {
                                    string es_aviso = "N";

                                    try { es_aviso = dtInfoRequerimientos.Rows[i]["ES_AVISO"].ToString(); }
                                    catch (Exception ex) { }

                                    var requerimiento = new xmlTransaccion.requerimiento()
                                    {
                                        numeroRequerimiento = dtInfoRequerimientos.Rows[i]["REQUERIMIENTO"].ToString(),
                                        poliza = dtInfoRequerimientos.Rows[i]["POLIZA"].ToString(),
                                        moneda = dtInfoRequerimientos.Rows[i]["MONEDA"].ToString(),
                                        totalRequerimiento = dtInfoRequerimientos.Rows[i]["TOTAL"].ToString(),
                                        vencimientoRequerimiento = dtInfoRequerimientos.Rows[i]["FECHA_VENCIMIENTO"].ToString(),
                                        nombrePagador = dtInfoRequerimientos.Rows[i]["PAGADOR"].ToString(),
                                        nitPagador = "C/F", //PENDIENTE
                                        sistema = xmlSolicitud.datos.identificadores.sistema,
                                        numeroCuota = dtInfoRequerimientos.Rows[i]["PAGO"].ToString(),
                                        estado = dtInfoRequerimientos.Rows[i]["STSFACT"].ToString(),
                                        idepol = dtInfoRequerimientos.Rows[i]["IDEPOL"].ToString(),
                                        fechaCobroRequerimiento = dtInfoRequerimientos.Rows[i]["FECHA_COBRO"].ToString(),
                                        asignaFactura = dtInfoRequerimientos.Rows[i]["ASIGNA_FACTURA"].ToString(),
                                        codFact = dtInfoRequerimientos.Rows[i]["CODFACT"].ToString(),
                                        email = dtInfoRequerimientos.Rows[i]["EMAIL"].ToString(),
                                        direc_cobro = dtInfoRequerimientos.Rows[i]["DIREC_COBRO"].ToString(),
                                        es_aviso = es_aviso
                                    };

                                    try { requerimiento.tip_docum = dtInfoRequerimientos.Rows[i]["TIP_DOCUM"].ToString(); } catch (Exception ex) { }
                                    try { requerimiento.cod_docum = dtInfoRequerimientos.Rows[i]["COD_DOCUM"].ToString(); } catch (Exception ex) { }

                                    clTransaccion.datos.requerimientos.requerimiento.Add(requerimiento);
                                }

                                clTransaccion.datos.encabezado.codigoRetorno = "00";
                                clTransaccion.datos.encabezado.mensajeRetorno = "CONSULTA REALIZADA CON EXITO.";
                            }
                            else
                            {
                                clTransaccion.datos.encabezado.codigoRetorno = "01";
                                clTransaccion.datos.encabezado.mensajeRetorno = "IDENTIFICADOR NO EXISTE";
                            }
                            #endregion Asignacion
                        }
                        //BUSQUEDA DE REQUERIMIENTOS POR IDEFACT
                        else
                        {
                            #region CONSULTA | REQUERIMIENTO
                            ConsultasReef consultasReef = new ConsultasReef();

                            string[] requerimientos = xmlSolicitud.datos.identificadores.requerimiento.Split(';');

                            clTransaccion.datos.requerimientos = new xmlTransaccion._requerimiento();
                            clTransaccion.datos.requerimientos.requerimiento = new List<xmlTransaccion.requerimiento>();

                            for (int i = 0; i < requerimientos.Count(); i++)
                            {
                                string noRequerimiento = requerimientos[i];

                                // Obtener datos de todos los sistemas
                                var datosRequerimientos = ObtenerRequerimientosTodosSistemas(noRequerimiento, acselDB, tronwebDB, consultasReef);

                                // Procesar resultados de todos los sistemas
                                ProcesarResultadosRequerimientos(datosRequerimientos, clTransaccion);
                            }

                            // Validar y establecer mensaje de encabezado unificado
                            ValidarYEstablecerMensajeEncabezado(clTransaccion);
                            clTransaccion.datos.identificadores.requerimiento = string.Join(";", requerimientos.ToArray());

                            #endregion CONSULTA | REQUERIMIENTO
                        }
                        break;

                    case "POLIZA":

                        #region CONSULTA | POLIZA

                        // Consultar primero en Reef
                        ConsultasReef consultaReef = new ConsultasReef();
                        DataTable dtInfoPolizaReef = consultaReef.ObtenerVigenciasPoliza(policyNumber: xmlSolicitud.datos.identificadores.numpol);

                        DataTable dtPolizasCombinadas = new DataTable();

                        // Verificar si Reef encontró resultados
                        if (dtInfoPolizaReef != null && dtInfoPolizaReef.Rows.Count > 0)
                        {
                            // Si Reef encontró datos, usar solo esos
                            dtPolizasCombinadas = dtInfoPolizaReef.Copy();
                        }
                        else
                        {
                            // Si Reef no encontró nada, buscar en AcselTron
                            DataTable dtPolizaAcselTron = acselDB.Core_Buscar_Poliza(xmlSolicitud.datos.identificadores.codpol,
                                                                                     xmlSolicitud.datos.identificadores.numpol,
                                                                                     xmlSolicitud.datos.identificadores.numcert);

                            if (dtPolizaAcselTron != null && dtPolizaAcselTron.Rows.Count > 0)
                            {
                                dtPolizasCombinadas = dtPolizaAcselTron.Copy();
                            }
                        }

                        // Verificar si se encontraron resultados
                        if (dtPolizasCombinadas.Rows.Count > 0)
                        {
                            string vJsonPolizas = JsonConvert.SerializeObject(dtPolizasCombinadas);
                            clTransaccion.datos.polizas.poliza = JsonConvert.DeserializeObject<List<xmlTransaccion.poliza>>(vJsonPolizas);

                            clTransaccion.datos.encabezado.codigoRetorno = "00";
                            clTransaccion.datos.encabezado.mensajeRetorno = "CONSULTA REALIZADA CON EXITO.";
                        }
                        else
                        {
                            clTransaccion.datos.encabezado.codigoRetorno = "01";
                            clTransaccion.datos.encabezado.mensajeRetorno = "IDENTIFICADOR NO EXISTE";
                        }

                        #endregion CONSULTA | POLIZA

                        break;
                    case "LIQUIDACION":

                        #region CONSULTA | LIQUIDACIONES

                        string[] liquidaciones = xmlSolicitud.datos.identificadores.requerimiento.Split(';');

                        for (int i = 0; i < liquidaciones.Count(); i++)
                        {
                            string noLiquidacion = liquidaciones[i];

                            // Obtener datos de ambos sistemas
                            var datosLiquidaciones = ObtenerLiquidaciones_Tron_Reef(noLiquidacion);

                            // Procesar resultados de ambos sistemas
                            ProcesarResultadosLiquidaciones(datosLiquidaciones, clTransaccion, noLiquidacion);
                        }

                        // Validar y establecer mensaje de encabezado unificado para liquidaciones
                        ValidarYEstablecerMensajeEncabezadoLiquidaciones(clTransaccion);
                        clTransaccion.datos.identificadores.requerimiento = string.Join(";", liquidaciones.ToArray());

                        #endregion CONSULTA | LIQUIDACIONES

                        break;

                    default:
                        break;
                }

                #endregion TIPO | BUSQUEDA

                #region GENERACION | XML

                resultado = clRespuesta.generarXmlTransaccion(clTransaccion);

                #endregion GENERACION | XML

            }
            catch (Exception ex)
            {
                Logger.LogErrorMessage(ex, "ConsultarTransaccion");
            }
            return resultado;
        }

        /// <summary>
        /// Proceso para pagar
        /// </summary>
        /// <param name="xmlSolicitud"></param>
        /// <param name="Bitacora"></param>
        /// <returns></returns>
        public XElement PagarRequerimientos(xmlTransaccion._transaccion xmlSolicitud, Estructuras.Bitacora_wsTransacLinea Bitacora)
        {
            XElement respuesta = null;
            Cores.MySQLLogger logger = new Cores.MySQLLogger();
            List<xmlTransaccion.requerimiento> LstReq_Bitacora = new List<xmlTransaccion.requerimiento>();
            List<xmlTransaccion.medio> lstMedio_Bitacora = new List<xmlTransaccion.medio>();
            float vTotal = 0;

            #region Instancias

            xmlTransaccion clRespuesta = new xmlTransaccion();
            xmlTransaccion._transaccion clTransaccion = new xmlTransaccion._transaccion();
            xmlTransaccion._estructuraTransaccion clEstructuraTransaccion = new xmlTransaccion._estructuraTransaccion();
            xmlTransaccion._encabezado clEncabezado = new xmlTransaccion._encabezado();
            xmlTransaccion._identificadores clIdentificadores = new xmlTransaccion._identificadores();
            xmlTransaccion._requerimiento clRequerimiento = new xmlTransaccion._requerimiento();
            List<xmlTransaccion.requerimiento> lstResumenReqs = new List<xmlTransaccion.requerimiento>();

            #endregion Instancias

            #region Encabezado

            clEncabezado.convenio = xmlSolicitud.datos.encabezado.convenio;
            clEncabezado.proveedor = xmlSolicitud.datos.encabezado.proveedor;
            clEncabezado.usuarioOrigen = xmlSolicitud.datos.encabezado.usuarioOrigen;
            clEncabezado.codigoCajero = xmlSolicitud.datos.encabezado.codigoCajero;
            clEncabezado.tipoBusqueda = xmlSolicitud.datos.encabezado.tipoBusqueda;
            clEncabezado.tipoTransaccion = xmlSolicitud.datos.encabezado.tipoTransaccion;
            clEncabezado.autorizacionProveedor = xmlSolicitud.datos.encabezado.autorizacionProveedor;
            clEncabezado.autorizacionBanco = xmlSolicitud.datos.encabezado.autorizacionBanco;
            clEncabezado.idTransaccion = Bitacora.ID_TRANSACCION.ToString();

            if (string.IsNullOrEmpty(xmlSolicitud.datos.encabezado.esAviso))
            {
                clEncabezado.esAviso = "N";
            }
            else
            {
                clEncabezado.esAviso = xmlSolicitud.datos.encabezado.esAviso;
            }


            #endregion Encabezado

            var vSistema = xmlSolicitud.datos.requerimientos.requerimiento.Select(x => x.sistema).Distinct().ToList();

            if (vSistema.Count() == 1)
            {
                DataSet dtsRespuesta = new DataSet();
                DataTable datosRespuesta = new DataTable();

                List<xmlPagoRequermiento.requerimiento> lstReqsAcsel = new List<xmlPagoRequermiento.requerimiento>();
                List<xmlCobroTron.requerimiento> lstReqsTron = new List<xmlCobroTron.requerimiento>();
                List<xmlCobroTron.requerimiento> lstReqsREEF = new List<xmlCobroTron.requerimiento>();

                Estructuras clEstructuras = new Estructuras();

                #region Obtener Requerimientos

                //se recorren los requerimientos que seran pagados
                for (int r = 0; r < xmlSolicitud.datos.requerimientos.requerimiento.Count; r++)
                {
                    Bitacora.SISTEMA = xmlSolicitud.datos.requerimientos.requerimiento[r].sistema;
                    switch (xmlSolicitud.datos.requerimientos.requerimiento[r].sistema)
                    {
                        case "A": //ACSEL
                            xmlPagoRequermiento.requerimiento reqAcsel = new xmlPagoRequermiento.requerimiento();
                            reqAcsel.req = xmlSolicitud.datos.requerimientos.requerimiento[r].numeroRequerimiento;

                            lstReqsAcsel.Add(reqAcsel);
                            break;

                        case "R": //REEF
                            xmlCobroTron.requerimiento reqREEF = new xmlCobroTron.requerimiento();
                            reqREEF.SERIE = "";
                            reqREEF.NUMEFACT = "";
                            reqREEF.NUMRECI = xmlSolicitud.datos.requerimientos.requerimiento[r].numeroRequerimiento;

                            lstReqsREEF.Add(reqREEF);
                            break;

                        case "T": //TRONWEB
                            xmlCobroTron.requerimiento reqTron = new xmlCobroTron.requerimiento();
                            reqTron.SERIE = "";
                            reqTron.NUMEFACT = "";
                            reqTron.NUMRECI = xmlSolicitud.datos.requerimientos.requerimiento[r].numeroRequerimiento;

                            lstReqsTron.Add(reqTron);
                            break;

                        default:
                            break;
                    }
                    LstReq_Bitacora.Add(xmlSolicitud.datos.requerimientos.requerimiento[r]);
                }

                #endregion Obtener Requerimientos

                Estructuras.Bitacora_DetalleWSTransacLinea Detalle_Bitacora = new Estructuras.Bitacora_DetalleWSTransacLinea();

                #region ACSEL

                if (lstReqsAcsel.Count > 0)
                {
                    Bitacora.SISTEMA = Constants.Systems.ACSEL;

                    List<xmlPagoRequermiento.documento_ingreso> lstDocIngAcsel = new List<xmlPagoRequermiento.documento_ingreso>();

                    #region Medios de pago

                    for (int d = 0; d < xmlSolicitud.datos.mediosPago.medio.Count; d++)
                    {
                        xmlPagoRequermiento.documento_ingreso docingAcsel = new xmlPagoRequermiento.documento_ingreso();

                        docingAcsel.CODENTFINAN = xmlSolicitud.datos.mediosPago.medio[d].codEntidadFinanciera;
                        docingAcsel.CODIGO = xmlSolicitud.datos.mediosPago.medio[d].tipoPago;//DEFINIR UN TIPO DE DOCING PARA LOS CLIENTES;
                        docingAcsel.MONEDA = xmlSolicitud.datos.mediosPago.medio[d].codMoneda;
                        docingAcsel.MONTO = xmlSolicitud.datos.mediosPago.medio[d].monto;
                        docingAcsel.NUMREFDOC = xmlSolicitud.datos.mediosPago.medio[d].numeroDocumento;
                        docingAcsel.NUMAUTORIZA = xmlSolicitud.datos.mediosPago.medio[d].numeroAutorizacion;
                        docingAcsel.SISTEMA = "";

                        Bitacora.MONEDA = xmlSolicitud.datos.mediosPago.medio[d].codMoneda; //GERROD: cambiar esto cuando se puedan pagar con varias monedas

                        try { vTotal += float.Parse(xmlSolicitud.datos.mediosPago.medio[d].monto, CultureInfo.InvariantCulture.NumberFormat); }
                        catch (Exception) { vTotal += 0; }

                        lstDocIngAcsel.Add(docingAcsel);
                        lstMedio_Bitacora.Add(xmlSolicitud.datos.mediosPago.medio[d]);
                    }

                    #endregion Medios de pago

                    #region Estructura de pago

                    Cores.AcselDB loggerA = new Cores.AcselDB();
                    xmlPagoRequermiento pagoAcsel = new xmlPagoRequermiento();
                    pagoAcsel.encabezado = new xmlPagoRequermiento._encabezado();

                    //SE ASIGNA EL CAJERO Y USUARIO PARA COBRAR
                    pagoAcsel.encabezado.CODIGO_CAJERO = xmlSolicitud.datos.encabezado.codigoCajero;
                    pagoAcsel.encabezado.USUARIO_ORIGEN = xmlSolicitud.datos.encabezado.usuarioOrigen;

                    pagoAcsel.requerimientos = lstReqsAcsel;
                    pagoAcsel.tiposPago = lstDocIngAcsel;

                    #endregion Estructura de pago

                    dtsRespuesta = new DataSet();
                    dtsRespuesta = loggerA.pagarAcsel(pagoAcsel.generarXMLPago(pagoAcsel), Bitacora);

                    //REALIZAR VALIDACIONES PARA EL RETORNO DE LA RESPUESTA
                    if (dtsRespuesta != null)
                    {
                        if (dtsRespuesta.Tables.Count > 0)
                        {
                            if (dtsRespuesta.Tables[0].Rows[0]["Codigo"].ToString() == "1")
                            {
                                #region Resumen de pago

                                //Se agregan los recibos cobrados para el resumen de pago.
                                for (int h = 0; h < dtsRespuesta.Tables[2].Rows.Count; h++)
                                {
                                    xmlTransaccion.requerimiento req = new xmlTransaccion.requerimiento();
                                    req.numeroRequerimiento = dtsRespuesta.Tables[2].Rows[h]["numeroRequerimiento"].ToString();
                                    req.codFact = dtsRespuesta.Tables[2].Rows[h]["codFact"].ToString();
                                    req.numFact = dtsRespuesta.Tables[2].Rows[h]["numFact"].ToString();
                                    req.cae = dtsRespuesta.Tables[2].Rows[h]["cae"].ToString();
                                    req.reling = dtsRespuesta.Tables[2].Rows[h]["reling"].ToString();

                                    lstResumenReqs.Add(req);
                                }

                                clEncabezado.codigoRetorno = "00";
                                clEncabezado.mensajeRetorno = "PAGO REALIZADO CON EXITO";

                                clRequerimiento.requerimiento = lstResumenReqs;

                                #endregion Resumen de pago

                                Bitacora.ERROR = "0";
                                Bitacora.MENSAJE = clEncabezado.mensajeRetorno;
                            }
                            else
                            {
                                clRequerimiento.requerimiento = lstResumenReqs;

                                clEncabezado.codigoRetorno = "01";
                                clEncabezado.mensajeRetorno = "ERROR AL REALIZAR EL PAGO";
                                Bitacora.ERROR = "1";
                                Bitacora.MENSAJE = clEncabezado.mensajeRetorno;
                            }
                        }
                        else
                        {
                            clRequerimiento.requerimiento = lstResumenReqs;

                            clEncabezado.codigoRetorno = "01";
                            clEncabezado.mensajeRetorno = "ERROR AL REALIZAR EL PAGO";
                            Bitacora.ERROR = "1";
                            Bitacora.MENSAJE = clEncabezado.mensajeRetorno;
                        }
                    }
                    else
                    {
                        clRequerimiento.requerimiento = lstResumenReqs;

                        clEncabezado.codigoRetorno = "01";
                        clEncabezado.mensajeRetorno = "ERROR AL REALIZAR EL PAGO";
                        Bitacora.ERROR = "1";
                        Bitacora.MENSAJE = clEncabezado.mensajeRetorno;
                    }
                }

                #endregion ACSEL

                #region TRONWEB

                if (lstReqsTron.Count > 0)
                {
                    Bitacora.SISTEMA = Constants.Systems.TRONWEB;

                    Cores.MySQLLogger loggerT = new Cores.MySQLLogger();
                    List<xmlCobroTron.documento_ingreso> listadoDocumentosPagoT = new List<xmlCobroTron.documento_ingreso>();

                    string remesaRecibo = string.Empty;

                    #region Medios de pago

                    for (int d = 0; d < xmlSolicitud.datos.mediosPago.medio.Count; d++)
                    {
                        xmlCobroTron.documento_ingreso docingTron = new xmlCobroTron.documento_ingreso();

                        docingTron.TIPOPAGO = xmlSolicitud.datos.mediosPago.medio[d].tipoPago;
                        docingTron.ENTFINAN = xmlSolicitud.datos.mediosPago.medio[d].codEntidadFinanciera;

                        switch (docingTron.TIPOPAGO)
                        {
                            case "TAR":
                                docingTron.NUMREF = xmlSolicitud.datos.mediosPago.medio[d].numeroReferencia;
                                docingTron.NUMREF_TAR = xmlSolicitud.datos.mediosPago.medio[d].numeroDocumento;
                                break;
                            default:
                                docingTron.NUMREF = xmlSolicitud.datos.mediosPago.medio[d].numeroDocumento;
                                docingTron.NUMREF_TAR = xmlSolicitud.datos.mediosPago.medio[d].numeroReferencia;
                                break;
                        }

                        docingTron.CODMONEDA = xmlSolicitud.datos.mediosPago.medio[d].codMoneda;
                        docingTron.MONTO = xmlSolicitud.datos.mediosPago.medio[d].monto;
                        docingTron.NUMAUTORIZA = xmlSolicitud.datos.mediosPago.medio[d].numeroAutorizacion;
                        docingTron.FECHA_CHQ = xmlSolicitud.datos.mediosPago.medio[d].fecha_cheque;
                        docingTron.TIPO_TAR = xmlSolicitud.datos.mediosPago.medio[d].tipoTarjeta;
                        docingTron.CODIGO_TAR = xmlSolicitud.datos.mediosPago.medio[d].codigoTarjeta;
                        docingTron.CUENTA_SIMPLIFICADA = xmlSolicitud.datos.mediosPago.medio[d].CUENTA_SIMPLIFICADA;
                        docingTron.DESCRIPCION = xmlSolicitud.datos.mediosPago.medio[d].DESCRIPCION;

                        Bitacora.MONEDA = xmlSolicitud.datos.mediosPago.medio[d].codMoneda; //GERROD: cambiar esto cuando se puedan pagar con varias monedas

                        try { vTotal += float.Parse(xmlSolicitud.datos.mediosPago.medio[d].monto, CultureInfo.InvariantCulture.NumberFormat); }
                        catch (Exception) { vTotal += 0; }

                        listadoDocumentosPagoT.Add(docingTron);
                        lstMedio_Bitacora.Add(xmlSolicitud.datos.mediosPago.medio[d]);
                    }

                    #endregion Medios de pago

                    #region Estructura de pago

                    xmlCobroTron pagoT = new xmlCobroTron();
                    xmlCobroTron._ENCABEZADO encabezadoT = new xmlCobroTron._ENCABEZADO();

                    //ENCABEZADO
                    encabezadoT.SISTEMA = "TRONWEB";
                    encabezadoT.EQUIPO = "0";
                    Bitacora.SISTEMA = Constants.Systems.TRONWEB;
                    encabezadoT.USUARIO_ORIGEN = clEncabezado.usuarioOrigen;
                    encabezadoT.CODIGO_CAJERO = clEncabezado.codigoCajero;
                    encabezadoT.ES_AVISO = clEncabezado.esAviso;

                    pagoT.requerimientos = lstReqsTron;
                    pagoT.tiposPago = listadoDocumentosPagoT;
                    pagoT.encabezado = encabezadoT;

                    #endregion Estructura de pago

                    dtsRespuesta = new DataSet();

                    #region ACTUALIZACION MONEDA US$
                    foreach (var tipo_pago in pagoT.tiposPago.Where(x => x.CODMONEDA == "US$"))
                    {
                        tipo_pago.CODMONEDA = "$";
                    }
                    #endregion

                    switch (vSistema.ToString())
                    {
                        case "T":
                            TronwebDB tronwebDB = new TronwebDB();
                            switch (xmlSolicitud.datos.encabezado.tipoBusqueda)
                            {
                                case "REQUERIMIENTO":
                                    dtsRespuesta = tronwebDB.CobrarTronWebv2(pagoT.generarXMLCobroV2(pagoT), Bitacora);
                                    break;

                                case "DEDUCIBLE":
                                    dtsRespuesta = tronwebDB.CobrarDeducibleT(pagoT.generarXMLCobroV2(pagoT), Bitacora);
                                    break;

                                default:
                                    break;
                            }
                            break;
                        default:
                            break;
                    }


                    //REALIZAR VALIDACIONES PARA EL RETORNO DE LA RESPUESTA
                    if (dtsRespuesta != null)
                    {
                        if (dtsRespuesta.Tables.Count > 0)
                        {
                            if (dtsRespuesta.Tables[0].Rows[0]["Codigo"].ToString() == "0")
                            {
                                #region Resumen de pago

                                for (int h = 0; h < dtsRespuesta.Tables[0].Rows.Count; h++)
                                {
                                    xmlTransaccion.requerimiento req = new xmlTransaccion.requerimiento();
                                    req.numeroRequerimiento = dtsRespuesta.Tables[0].Rows[h]["Numereci"].ToString();
                                    req.codFact = dtsRespuesta.Tables[0].Rows[h]["Numereci"].ToString();
                                    req.numFact = dtsRespuesta.Tables[0].Rows[h]["Numereci"].ToString();
                                    req.cae = dtsRespuesta.Tables[0].Rows[h]["Cae"].ToString();
                                    req.reling = dtsRespuesta.Tables[0].Rows[h]["Idcobro"].ToString();

                                    lstResumenReqs.Add(req);
                                }
                                clEncabezado.codigoRetorno = "00";
                                clEncabezado.mensajeRetorno = "PAGO REALIZADO CON EXITO";
                                clRequerimiento.requerimiento = lstResumenReqs;

                                #endregion Resumen de pago

                                #region Bitacora
                                Bitacora.ERROR = "0";
                                Bitacora.MENSAJE = clEncabezado.mensajeRetorno;
                                #endregion Bitacora
                            }
                            else
                            {
                                clRequerimiento.requerimiento = lstResumenReqs;

                                clEncabezado.codigoRetorno = "01";
                                clEncabezado.mensajeRetorno = "ERROR AL REALIZAR EL PAGO";

                                Bitacora.ERROR = "1";
                                Bitacora.MENSAJE = clEncabezado.mensajeRetorno;
                            }
                        }
                        else
                        {
                            clRequerimiento.requerimiento = lstResumenReqs;

                            clEncabezado.codigoRetorno = "01";
                            clEncabezado.mensajeRetorno = "ERROR AL REALIZAR EL PAGO";
                            Bitacora.ERROR = "1";
                            Bitacora.MENSAJE = clEncabezado.mensajeRetorno;
                        }
                    }
                    else
                    {
                        clRequerimiento.requerimiento = lstResumenReqs;

                        clEncabezado.codigoRetorno = "01";
                        clEncabezado.mensajeRetorno = "ERROR AL REALIZAR EL PAGO";
                        Bitacora.MENSAJE = clEncabezado.mensajeRetorno;
                        Bitacora.ERROR = "1";
                    }
                }

                #endregion TRONWEB

                #region REEF
                if (lstReqsREEF.Count > 0)
                {
                    Bitacora.SISTEMA = Constants.Systems.REEF;

                    try
                    {
                        var reefPaymentService = new Reef.Services.ReefPaymentService();

                        var taskResultado = reefPaymentService.ProcesarPagoDigitalAsync(
                            requerimientos: lstReqsREEF,
                            mediosPago: xmlSolicitud.datos.mediosPago.medio,
                            encabezado: clEncabezado
                        );

                        taskResultado.Wait();
                        var resultadoPago = taskResultado.Result;

                        if (resultadoPago)
                        {
                            foreach (var recibo in lstReqsREEF)
                            {
                                lstResumenReqs.Add(new xmlTransaccion.requerimiento()
                                {
                                    numeroRequerimiento = recibo.NUMRECI
                                });
                            }

                            clEncabezado.codigoRetorno = "00";
                            clEncabezado.mensajeRetorno = "PAGO REALIZADO CON EXITO EN REEF";
                            clRequerimiento.requerimiento = lstResumenReqs;

                            Bitacora.ERROR = "0";
                            Bitacora.MENSAJE = clEncabezado.mensajeRetorno;
                        }
                        else
                        {
                            // Procesar respuesta con error
                            clRequerimiento.requerimiento = lstResumenReqs;
                            clEncabezado.codigoRetorno = "01";
                            clEncabezado.mensajeRetorno = $"ERROR AL REALIZAR EL PAGO EN REEF: {resultadoPago}";//PENDIENTE MOSTRAR ERROR

                            Bitacora.ERROR = "1";
                            Bitacora.MENSAJE = clEncabezado.mensajeRetorno;
                        }
                    }
                    catch (Exception ex)
                    {
                        clRequerimiento.requerimiento = lstResumenReqs;
                        clEncabezado.codigoRetorno = "01";
                        clEncabezado.mensajeRetorno = $"ERROR INESPERADO AL PROCESAR PAGO REEF: {ex.Message}";

                        Bitacora.ERROR = "1";
                        Bitacora.MENSAJE = clEncabezado.mensajeRetorno;

                        // Log del error para debugging
                        Console.WriteLine($"Error en procesamiento REEF: {ex}");
                    }
                }
                #endregion
            }
            else
            {
                //FALTA CODIGO PARA COBRAR 2 SISTEMAS AL MISMO TIEMPO
            }

            #region Estructura Respuesta

            clEstructuraTransaccion.encabezado = clEncabezado;
            clEstructuraTransaccion.identificadores = clIdentificadores;
            clEstructuraTransaccion.requerimientos = clRequerimiento;
            clTransaccion.datos = clEstructuraTransaccion;

            #endregion Estructura Respuesta

            try
            {
                try
                {
                    //validamos si hay errores para devolver el mensaje de error a los clientes que consumen
                    if (clEncabezado.codigoRetorno == "01")
                    {
                        DataTable erroresTransaccion = new DataTable();

                        erroresTransaccion = logger.obtenerErroresTransacciones(clEncabezado.idTransaccion);

                        if (erroresTransaccion.Rows.Count > 0)
                        {
                            clEncabezado.mensajeRetorno = erroresTransaccion.Rows[0]["mensaje"].ToString();
                        }
                    }
                }
                catch (Exception) { }

                respuesta = clRespuesta.generarXmlTransaccion(clTransaccion);

                Bitacora.XML_OUT = respuesta.ToString();
                Bitacora.TOTAL = vTotal.ToString("0.00");
                logger.Update_Bitacora(Bitacora, LstReq_Bitacora, lstMedio_Bitacora);
            }
            catch (Exception) { }

            return respuesta;
        }

        /// <summary>
        /// Proceso para Remesar un recibo
        /// </summary>
        /// <param name="xmlSolicitud"></param>
        /// <param name="Bitacora"></param>
        /// <returns></returns>
        public XElement RemesarRequerimientos(xmlTransaccion._transaccion xmlSolicitud, Estructuras.Bitacora_wsTransacLinea Bitacora)
        {
            #region instancias

            Cores.MySQLLogger operacion = new Cores.MySQLLogger();
            XElement resultado = null;
            xmlTransaccion clRespuesta = new xmlTransaccion();
            xmlTransaccion._transaccion clTransaccion = new xmlTransaccion._transaccion();
            clTransaccion.datos = new xmlTransaccion._estructuraTransaccion();
            clTransaccion.datos.encabezado = new xmlTransaccion._encabezado();
            clTransaccion.datos.polizas = new xmlTransaccion._poliza();
            clTransaccion.datos.polizas.poliza = new List<xmlTransaccion.poliza>();
            clTransaccion.datos.requerimientos = new xmlTransaccion._requerimiento();
            clTransaccion.datos.requerimientos.requerimiento = new List<xmlTransaccion.requerimiento>();
            clTransaccion.datos.identificadores = new xmlTransaccion._identificadores();
            clTransaccion.datos.mediosPago = new xmlTransaccion._medio();
            clTransaccion.datos.mediosPago.medio = new List<xmlTransaccion.medio>();

            #endregion instancias

            #region Encabezado

            clTransaccion.datos.encabezado = xmlSolicitud.datos.encabezado;
            clTransaccion.datos.encabezado.idTransaccion = Bitacora.ID_TRANSACCION.ToString();
            clTransaccion.datos.identificadores = xmlSolicitud.datos.identificadores;

            #endregion Encabezado

            Bitacora.SISTEMA_USO = xmlSolicitud.datos.encabezado.sistemaUso;
            Bitacora.METODO = "reversarRequerimientos";
            xmlSolicitud.datos.encabezado.idReferencia = Bitacora.ID_TRANSACCION.ToString();

            try
            {
                Bitacora.ERROR = "0";
                Bitacora.MENSAJE = "Se envio correo de reversión.";

                clTransaccion.datos.encabezado.codigoRetorno = "00";
                clTransaccion.datos.encabezado.mensajeRetorno = "REVERSION REALIZADA CON EXITO.";

                //se envia el correo en background
                Task.Factory.StartNew(() => notificaReversionReq(xmlSolicitud),
                    CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default);
            }
            catch (Exception ex)
            {
                Bitacora.ERROR = "0";
                Bitacora.MENSAJE = "No se envio correo de reversión. " + ex.Message;

                clTransaccion.datos.encabezado.codigoRetorno = "01";
                clTransaccion.datos.encabezado.mensajeRetorno = "ERROR EN REVERSION DE PAGO.";
            }

            #region GENERACION | XML

            resultado = clRespuesta.generarXmlTransaccion(clTransaccion);

            #endregion GENERACION | XML

            Bitacora.XML_OUT = resultado.ToString();
            operacion.Update_Bitacora(Bitacora);

            return resultado;
        }

        #endregion Public Methods

        #region Private Methods

        private bool notificaReversionReq(xmlTransaccion._transaccion datosReversion)
        {
            bool resp = false;

            WSComunica.Ienviar_correoClient ws = null;

            for (int r = 0; r < datosReversion.datos.requerimientos.requerimiento.Count; r++)
            {
                using (ws = new WSComunica.Ienviar_correoClient())
                {
                    try
                    {
                        WSComunica.dato_correo datos = new WSComunica.dato_correo();
                        ws.InnerChannel.OperationTimeout = new TimeSpan(0, 5, 0);
                        //datos.usuario = "WSTransacGT";
                        datos.id_contenido = "REV_REQ";
                        //datos.asunto = "WSTRASACGT - REVERSIÓN DE PAGO";
                        datos.html = true;

                        datos.parametro = new Dictionary<string, string>();

                        datos.parametro.Add("[$no_convenio]", datosReversion.datos.encabezado.convenio);
                        datos.parametro.Add("[$no_proveedor]", datosReversion.datos.encabezado.proveedor);
                        datos.parametro.Add("[$no_auto_banco]", datosReversion.datos.encabezado.autorizacionBanco);
                        datos.parametro.Add("[$no_requerimiento]", datosReversion.datos.requerimientos.requerimiento[r].numeroRequerimiento);
                        datos.parametro.Add("[$no_transaccion]", datosReversion.datos.encabezado.idReferencia);

                        try
                        {
                            JObject json = new JObject();
                            ws.Open();
                            json = JObject.Parse(ws.doEnviar("NOTIFICAR_WEB", datos));
                            ws.Close();
                            resp = (bool)json.GetValue("estado");
                        }
                        catch (WebException e)
                        {
                            if (e.Status == WebExceptionStatus.Timeout)
                            {
                                resp = true;
                            }
                            ws.Close();
                        }
                    }
                    finally
                    {
                        if (ws.State == CommunicationState.Opened)
                            ws.Close();
                        if (ws.State == CommunicationState.Faulted)
                            ws.Abort();
                    }
                }
            }

            return resp;
        }

        /// <summary>
        /// Obtiene requerimientos de todos los sistemas (AcselTron, TronWeb y Reef) para un número de requerimiento específico
        /// </summary>
        /// <param name="noRequerimiento">Número de requerimiento a buscar</param>
        /// <param name="acselDB">Instancia de AcselDB para consultas en Acsel</param>
        /// <param name="tronwebDB">Instancia de TronwebDB para consultas en TronWeb</param>
        /// <param name="consultasReef">Instancia de ConsultasReef para consultas en Reef</param>
        /// <returns>Tupla con los DataTables de resultados de cada sistema</returns>
        private (DataTable AcselTron, DataTable TronWeb, DataTable Reef) ObtenerRequerimientosTodosSistemas(string noRequerimiento, Cores.AcselDB acselDB, TronwebDB tronwebDB, ConsultasReef consultasReef)
        {
            DataTable dtRequerimientosACSEL = new DataTable();
            DataTable dtRequerimientosTRONWEB = new DataTable();
            DataTable dtRequerimientosREEF = new DataTable();

            // Validar sistema del requerimiento para Acsel y TronWeb
            List<Tuple<string, string, string>> InfoDocs = tronwebDB.ValidaSistemaRequerimiento(noRequerimiento);

            for (int j = 0; j < InfoDocs.Count; j++)
            {
                string _sistema = InfoDocs[j].Item1;
                string _requerimiento = InfoDocs[j].Item2;
                string _es_aviso = InfoDocs[j].Item3;

                if (!string.IsNullOrEmpty(_sistema))
                {
                    switch (_sistema)
                    {
                        case "A":
                            dtRequerimientosACSEL = acselDB.buscarRequerimientosAcsel(pIdePol: null,
                                                                                      pIdeFact: _requerimiento,
                                                                                      pNumcert: null);
                            break;
                        case "T":
                            dtRequerimientosTRONWEB = tronwebDB.BuscarRequerimientos_TW(pNoDocto: _requerimiento,
                                                                                        pEsAviso: _es_aviso);
                            break;
                    }
                }
            }

            // Siempre consultar en Reef
            dtRequerimientosREEF = consultasReef.ObtenerDocumentoPago(pymDcmVal: noRequerimiento);

            return (dtRequerimientosACSEL, dtRequerimientosTRONWEB, dtRequerimientosREEF);
        }

        /// <summary>
        /// Procesa los resultados de requerimientos de todos los sistemas y los agrega a la transacción
        /// </summary>
        /// <param name="datosRequerimientos">Tupla con los DataTables de resultados de cada sistema</param>
        /// <param name="clTransaccion">Objeto de transacción donde se agregarán los requerimientos</param>
        private void ProcesarResultadosRequerimientos((DataTable AcselTron, DataTable TronWeb, DataTable Reef) datosRequerimientos, xmlTransaccion._transaccion clTransaccion)
        {
            // Procesar resultados de Acsel
            ProcesarRequerimientosSistema(datosRequerimientos.AcselTron, Constants.Systems.ACSEL, clTransaccion);

            // Procesar resultados de TronWeb
            ProcesarRequerimientosSistema(datosRequerimientos.TronWeb, Constants.Systems.TRONWEB, clTransaccion);

            // Procesar resultados de Reef
            ProcesarRequerimientosSistema(datosRequerimientos.Reef, Constants.Systems.REEF, clTransaccion);
        }

        /// <summary>
        /// Procesa los requerimientos de un sistema específico y los agrega a la transacción
        /// </summary>
        /// <param name="dtRequerimientos">DataTable con los requerimientos del sistema</param>
        /// <param name="sistema">Sistema de origen (A, T, R)</param>
        /// <param name="clTransaccion">Objeto de transacción donde se agregarán los requerimientos</param>
        private void ProcesarRequerimientosSistema(DataTable dtRequerimientos, string sistema, xmlTransaccion._transaccion clTransaccion)
        {
            for (int j = 0; j < dtRequerimientos.Rows.Count; j++)
            {
                var requerimiento = new xmlTransaccion.requerimiento()
                {
                    numeroRequerimiento = dtRequerimientos.Rows[j]["REQUERIMIENTO"].ToString(),
                    poliza = dtRequerimientos.Rows[j]["POLIZA"].ToString(),
                    moneda = dtRequerimientos.Rows[j]["MONEDA"].ToString(),
                    totalRequerimiento = dtRequerimientos.Rows[j]["TOTAL"].ToString(),
                    vencimientoRequerimiento = dtRequerimientos.Rows[j]["FECHA_VENCIMIENTO"].ToString(),
                    nombrePagador = dtRequerimientos.Rows[j]["PAGADOR"].ToString(),
                    sistema = sistema,
                    numeroCuota = dtRequerimientos.Rows[j]["PAGO"].ToString(),
                    estado = dtRequerimientos.Rows[j]["STSFACT"].ToString(),
                    idepol = dtRequerimientos.Rows[j]["IDEPOL"].ToString(),
                    fechaCobroRequerimiento = dtRequerimientos.Rows[j]["FECHA_COBRO"].ToString(),
                    asignaFactura = dtRequerimientos.Rows[j]["ASIGNA_FACTURA"].ToString(),
                    codFact = dtRequerimientos.Rows[j]["CODFACT"].ToString(),
                    email = dtRequerimientos.Rows[j]["EMAIL"].ToString(),
                    direc_cobro = dtRequerimientos.Rows[j]["DIREC_COBRO"].ToString()
                };

                try { requerimiento.tip_docum = dtRequerimientos.Rows[j]["TIP_DOCUM"].ToString(); } catch (Exception ex) { }
                try { requerimiento.cod_docum = dtRequerimientos.Rows[j]["COD_DOCUM"].ToString(); } catch (Exception ex) { }

                // Configuraciones específicas por sistema
                switch (sistema)
                {
                    case "A":
                        requerimiento.id_moneda = dtRequerimientos.Rows[j]["MONEDA"].ToString(); // TEMPORAL para ACSEL
                        requerimiento.es_aviso = "N";
                        break;
                    case "T":
                    case "R":
                        requerimiento.id_moneda = dtRequerimientos.Rows[j]["ID_MONEDA"].ToString();
                        requerimiento.es_aviso = dtRequerimientos.Rows[j]["ES_AVISO"].ToString();
                        break;
                }

                clTransaccion.datos.requerimientos.requerimiento.Add(requerimiento);
            }
        }

        /// <summary>
        /// Valida el estado de los requerimientos y establece el mensaje de encabezado unificado para todos los sistemas
        /// </summary>
        /// <param name="clTransaccion">Objeto de transacción a validar</param>
        private void ValidarYEstablecerMensajeEncabezado(xmlTransaccion._transaccion clTransaccion)
        {
            if (clTransaccion.datos.requerimientos.requerimiento.Count > 0)
            {
                // Verificar si algún requerimiento no tiene saldo pendiente
                bool tieneSaldoPendiente = true;

                foreach (var req in clTransaccion.datos.requerimientos.requerimiento)
                {
                    // Validación unificada para todos los sistemas
                    if (EsRequerimientoSinSaldo(req.estado, req.totalRequerimiento, req.sistema))
                    {
                        tieneSaldoPendiente = false;
                        break;
                    }
                }

                if (!tieneSaldoPendiente)
                {
                    clTransaccion.datos.encabezado.codigoRetorno = "03";
                    clTransaccion.datos.encabezado.mensajeRetorno = "NO TIENE SALDO PENDIENTE.";
                }
                else
                {
                    clTransaccion.datos.encabezado.codigoRetorno = "00";
                    clTransaccion.datos.encabezado.mensajeRetorno = "CONSULTA REALIZADA CON EXITO.";
                }
            }
            else
            {
                clTransaccion.datos.encabezado.codigoRetorno = "01";
                clTransaccion.datos.encabezado.mensajeRetorno = "IDENTIFICADOR NO EXISTE";
            }
        }

        /// <summary>
        /// Determina si un requerimiento no tiene saldo pendiente basado en su estado y sistema
        /// </summary>
        /// <param name="estado">Estado del requerimiento</param>
        /// <param name="total">Total del requerimiento</param>
        /// <param name="sistema">Sistema de origen</param>
        /// <returns>True si no tiene saldo pendiente, False en caso contrario</returns>
        private bool EsRequerimientoSinSaldo(string estado, string total, string sistema)
        {
            switch (sistema)
            {
                case "A":
                    return estado == "COB";
                case "T":
                case "R":
                    return estado == "CT" || total == "0";
                default:
                    return false;
            }
        }

        /// <summary>
        /// Obtiene liquidaciones de ambos sistemas (TronWeb y Reef) para un número de liquidación específico
        /// </summary>
        /// <param name="noLiquidacion">Número de liquidación a buscar</param>
        /// <returns>Tupla con los datos de TronWeb y Reef</returns>
        private (DataTable TronWeb, DeducibleInfoStructure Reef) ObtenerLiquidaciones_Tron_Reef(string noLiquidacion)
        {
            DataTable dtLiquidacionesTronWeb = new DataTable();
            DeducibleInfoStructure datosLiquidacionReef = null;

            try
            {
                // Consultar primero en TronWeb
                TronwebDB tronwebDB = new TronwebDB();
                dtLiquidacionesTronWeb = tronwebDB.ConsultarLiquidaciones(noLiquidacion);

                // Consultar en Reef
                DeducibleReef deducibleReef = new DeducibleReef();
                datosLiquidacionReef = deducibleReef.consultarLiquidaciones(noLiquidacion);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al obtener liquidaciones para {noLiquidacion}: {ex.Message}");
            }

            return (dtLiquidacionesTronWeb, datosLiquidacionReef);
        }

        /// <summary>
        /// Procesa los resultados de liquidaciones de ambos sistemas y los agrega a la transacción
        /// </summary>
        /// <param name="datosLiquidaciones">Tupla con los datos de TronWeb y Reef</param>
        /// <param name="clTransaccion">Objeto de transacción donde se agregarán las liquidaciones</param>
        /// <param name="noLiquidacion">Número de liquidación original para casos de error</param>
        private void ProcesarResultadosLiquidaciones((DataTable TronWeb, DeducibleInfoStructure Reef) datosLiquidaciones, xmlTransaccion._transaccion clTransaccion, string noLiquidacion)
        {
            bool encontroResultados = false;

            // Procesar resultados de TronWeb
            if (datosLiquidaciones.TronWeb != null && datosLiquidaciones.TronWeb.Rows.Count > 0)
            {
                ProcesarLiquidacionesTronWeb(datosLiquidaciones.TronWeb, clTransaccion);
                encontroResultados = true;
            }

            // Procesar resultados de Reef
            if (datosLiquidaciones.Reef != null)
            {
                ProcesarLiquidacionReef(datosLiquidaciones.Reef, clTransaccion);
                encontroResultados = true;
            }

            // Si no se encontraron resultados en ningún sistema, agregar entrada vacía
            if (!encontroResultados)
            {
                clTransaccion.datos.requerimientos.requerimiento.Add(new xmlTransaccion.requerimiento()
                {
                    numeroRequerimiento = noLiquidacion
                });
            }
        }

        /// <summary>
        /// Procesa las liquidaciones de TronWeb y las agrega a la transacción
        /// </summary>
        /// <param name="dtLiquidaciones">DataTable con las liquidaciones de TronWeb</param>
        /// <param name="clTransaccion">Objeto de transacción donde se agregarán las liquidaciones</param>
        private void ProcesarLiquidacionesTronWeb(DataTable dtLiquidaciones, xmlTransaccion._transaccion clTransaccion)
        {
            for (int j = 0; j < dtLiquidaciones.Rows.Count; j++)
            {
                try
                {
                    clTransaccion.datos.requerimientos.requerimiento.Add(new xmlTransaccion.requerimiento()
                    {
                        numeroRequerimiento = dtLiquidaciones.Rows[j]["NUM_LIQ"].ToString(),
                        poliza = dtLiquidaciones.Rows[j]["NUM_POLIZA"].ToString(),
                        moneda = dtLiquidaciones.Rows[j]["COD_MON_LIQ_ISO"].ToString(),
                        totalRequerimiento = dtLiquidaciones.Rows[j]["IMP_LIQ_NETO"].ToString(),
                        vencimientoRequerimiento = dtLiquidaciones.Rows[j]["FEC_EST_PAGO"].ToString(),
                        nombrePagador = dtLiquidaciones.Rows[j]["NOM_TERCERO"].ToString(),
                        nitPagador = dtLiquidaciones.Rows[j]["COD_DOCUM"].ToString(),
                        sistema = Constants.Systems.TRONWEB,
                        numeroCuota = "1",
                        estado = string.IsNullOrEmpty(dtLiquidaciones.Rows[j]["FEC_PAGO"].ToString()) ? "EP" : "CT",
                        idepol = dtLiquidaciones.Rows[j]["NUM_POLIZA"].ToString(),
                        fechaCobroRequerimiento = dtLiquidaciones.Rows[j]["FEC_PAGO"].ToString(),
                        asignaFactura = "N"
                    });
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error al procesar liquidación TronWeb: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Procesa la liquidación de Reef y la agrega a la transacción
        /// </summary>
        /// <param name="datosLiquidacion">Estructura con los datos de la liquidación de Reef</param>
        /// <param name="clTransaccion">Objeto de transacción donde se agregará la liquidación</param>
        private void ProcesarLiquidacionReef(DeducibleInfoStructure datosLiquidacion, xmlTransaccion._transaccion clTransaccion)
        {
            try
            {
                clTransaccion.datos.requerimientos.requerimiento.Add(new xmlTransaccion.requerimiento()
                {
                    numeroRequerimiento = datosLiquidacion.NumLiq.ToString(),
                    poliza = datosLiquidacion.NumPoliza.ToString(),
                    moneda = datosLiquidacion.CodMonLiqIso.ToString(),
                    totalRequerimiento = datosLiquidacion.ImpLiqNeto.ToString(),
                    vencimientoRequerimiento = datosLiquidacion.FecEstPago.ToString(),
                    nombrePagador = datosLiquidacion.NomTercero.ToString(),
                    nitPagador = datosLiquidacion.CodDocum.ToString(),
                    sistema = Constants.Systems.REEF,
                    numeroCuota = "1",
                    estado = string.IsNullOrEmpty(datosLiquidacion.FecPago) ? "EP" : "CT",
                    idepol = datosLiquidacion.NumPoliza.ToString(),
                    fechaCobroRequerimiento = datosLiquidacion.FecPago,
                    asignaFactura = "N"
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al procesar liquidación Reef: {ex.Message}");
            }
        }

        /// <summary>
        /// Valida el estado de las liquidaciones y establece el mensaje de encabezado unificado
        /// </summary>
        /// <param name="clTransaccion">Objeto de transacción a validar</param>
        private void ValidarYEstablecerMensajeEncabezadoLiquidaciones(xmlTransaccion._transaccion clTransaccion)
        {
            if (clTransaccion.datos.requerimientos.requerimiento.Count > 0)
            {
                // Verificar si hay liquidaciones válidas (con datos completos)
                bool tieneResultadosValidos = clTransaccion.datos.requerimientos.requerimiento
                    .Any(req => !string.IsNullOrEmpty(req.poliza) && !string.IsNullOrEmpty(req.totalRequerimiento));

                if (tieneResultadosValidos)
                {
                    clTransaccion.datos.encabezado.codigoRetorno = "00";
                    clTransaccion.datos.encabezado.mensajeRetorno = "CONSULTA REALIZADA CON EXITO.";
                }
                else
                {
                    clTransaccion.datos.encabezado.codigoRetorno = "01";
                    clTransaccion.datos.encabezado.mensajeRetorno = "IDENTIFICADOR NO EXISTE";
                }
            }
            else
            {
                clTransaccion.datos.encabezado.codigoRetorno = "01";
                clTransaccion.datos.encabezado.mensajeRetorno = "IDENTIFICADOR NO EXISTE";
            }
        }

        #endregion Private Methods
    }
}